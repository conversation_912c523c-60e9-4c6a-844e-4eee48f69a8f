# Project Brief: Breaktime

*Do<PERSON><PERSON> ini adalah titik awal untuk memahami aplikasi manajemen Pijat Breaktime.*

## Tujuan Utama

Membuat aplikasi dashboard berbasis web untuk mengelola operasi bisnis Pijat Breaktime, termasuk manajemen outlet, terapis, layanan pijat, pelanggan, booking, transaksi, dan pelaporan.

## Lingkup Proyek

- Manajemen Pengguna & Hak Akses (Login, Roles, Permissions)
- Manajemen Outlet Pijat
- Manajemen Terapis (termasuk Komisi)
- Manajemen Layanan Pijat
- Manajemen Pelanggan (termasuk Poin & Tag)
- Manajemen Booking/Reservasi Pijat
- Manajemen Transaksi (termasuk Pembayaran Split & Struk)
- Pelaporan (Pendapatan Harian, Transaksi per Jam, Performa Terapis)
- Pengaturan Aplikasi (Tipe Pembayaran, dll.)
- Fitur Cek Poin Pelanggan

## Pengguna Target

- Staf Operasional Pijat (Kasir, Resepsionis)
- <PERSON><PERSON><PERSON>
- <PERSON><PERSON><PERSON>let/<PERSON><PERSON>
- Administrator <PERSON><PERSON><PERSON>
- Mungkin Investor

## <PERSON><PERSON><PERSON><PERSON>

- **Fungsional**: CRUD untuk semua entitas inti (Outlet, Terapis, Layanan, Pelanggan, Booking, Transaksi, Pengguna), Sistem autentikasi & otorisasi, Mekanisme booking pijat, Pencatatan transaksi & pembayaran, Perhitungan komisi terapis, Pelaporan bisnis, Pencetakan struk, Manajemen pengaturan.
- **Non-Fungsional**: Antarmuka yang mudah digunakan untuk operasional pijat, Responsif, Aman, Performa yang baik.