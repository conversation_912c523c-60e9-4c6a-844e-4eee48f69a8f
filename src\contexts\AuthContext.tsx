'use client';

import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';

// Tipe data untuk user
interface User {
  id: string;
  name: string;
  email: string;
  role: string;
}

// Tipe data untuk context
interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  authError: string | null;
  login: (username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
}

// Nilai default untuk context
const defaultContextValue: AuthContextType = {
  user: null,
  isLoading: true,
  isAuthenticated: false,
  authError: null,
  login: async () => {},
  logout: async () => {},
};

// Buat context
const AuthContext = createContext<AuthContextType>(defaultContextValue);

// Hook untuk menggunakan auth context
export const useAuth = () => useContext(AuthContext);

// Provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [authError, setAuthError] = useState<string | null>(null);
  const router = useRouter();

  // Cek status autentikasi saat komponen dimuat
  useEffect(() => {
    const checkAuth = async () => {
      try {
        setIsLoading(true);
        setAuthError(null);
        
        // Coba ambil data user dari session/API
        console.log('Checking auth status...');
        
        const response = await fetch('/api/auth/me', {
          credentials: 'include', // Penting untuk menyertakan cookies
        });
        
        console.log('Auth check status:', response.status);
        
        if (response.ok) {
          try {
            const text = await response.text();
            if (!text) {
              throw new Error('Respons kosong dari server');
            }
            
            const data = JSON.parse(text);
            setUser(data.user);
            console.log('User authenticated:', data.user?.name);
          } catch (parseError) {
            console.error('Error parsing auth response:', parseError);
            setUser(null);
            setAuthError('Format respons tidak valid dari server');
          }
        } else {
          console.log('User not authenticated');
          setUser(null);
        }
      } catch (error) {
        console.error('Error checking auth status:', error);
        setUser(null);
        setAuthError('Gagal memeriksa status autentikasi');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Fungsi login
  const login = async (username: string, password: string) => {
    setIsLoading(true);
    
    try {
      console.log('Memulai proses login untuk:', username);
      
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
        credentials: 'include', // Penting untuk menyertakan cookies
      });
      
      console.log('Status respons login:', response.status);
      
      let data;
      try {
        // Coba parse response sebagai JSON
        const text = await response.text();
        console.log('Response text:', text.substring(0, 100) + (text.length > 100 ? '...' : ''));
        
        if (!text) {
          throw new Error('Respons kosong dari server');
        }
        
        data = JSON.parse(text);
      } catch (parseError) {
        console.error('Error parsing JSON:', parseError);
        throw new Error('Format respons tidak valid dari server');
      }
      
      if (!response.ok) {
        throw new Error(data?.error || 'Login gagal');
      }
      
      setUser(data.user);
      return data;
    } catch (error) {
      console.error('Login error:', error);
      if (error instanceof Error) {
        throw new Error(error.message);
      }
      throw new Error('Terjadi kesalahan saat login');
    } finally {
      setIsLoading(false);
    }
  };

  // Fungsi logout
  const logout = async () => {
    setIsLoading(true);
    
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
      });
      
      setUser(null);
      router.push('/auth/login');
    } catch (error) {
      console.error('Error during logout:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const value = {
    user,
    isLoading,
    isAuthenticated: !!user,
    authError,
    login,
    logout,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
} 