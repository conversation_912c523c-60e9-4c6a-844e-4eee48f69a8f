# Konteks Produk: Breaktime

*Dokumen ini menjelaskan 'mengapa' di balik aplikasi Pijat Breaktime dan pengalaman pengguna yang diinginkan.*

## Masalah yang Diselesaikan

Mengelola operasional harian tempat pijat (seperti reservasi, j<PERSON><PERSON> terapis, data pelanggan, trans<PERSON><PERSON>, dan komisi) secara manual atau dengan sistem terpisah bisa rumit, memakan waktu, dan rentan kesalahan.

## Solusi yang Ditawarkan

Aplikasi **Breaktime** menyediakan platform dashboard terpusat untuk mengelola semua aspek operasional utama bisnis pijat: mulai dari penjadwalan booking, manajemen data pelanggan dan terapis, pencatatan transaksi dan pembayaran, perhitung<PERSON> komisi, hingga pelaporan. Ini bertujuan untuk meningkatkan efisiensi operasional, mengurangi kesalahan manual, dan memberikan wawasan bisnis yang lebih baik kepada manajemen.

## Cara <PERSON>rja (User Flow Umum)

1.  **Login**: Pengguna (Staf/Admin/Manajer) masuk ke sistem Breaktime.
2.  **Pemilihan Outlet** (Ji<PERSON> berlaku): Pengguna memilih outlet pijat tempat mereka bekerja.
3.  **Dashboard Utama**: Menampilkan ringkasan informasi penting (misalnya, booking hari ini, terapis yang tersedia, statistik pendapatan cepat).
4.  **Navigasi Fitur**: Pengguna menavigasi ke modul spesifik (misalnya, Pelanggan, Booking, Transaksi, Terapis, Laporan) melalui menu.
5.  **Melakukan Aksi**: Pengguna melakukan tugas seperti menambah pelanggan baru, membuat booking pijat, mencatat transaksi setelah layanan selesai, melihat laporan pendapatan atau komisi terapis.
6.  **Logout**: Pengguna keluar dari sistem.

## Tujuan Pengalaman Pengguna (UX)

- **Intuitif**: Mudah dipelajari dan digunakan oleh staf kasir, resepsionis, dan manajer tempat pijat.
- **Efisien**: Mempercepat proses booking, checkout transaksi, dan tugas administratif lainnya.
- **Informatif**: Menyajikan data operasional dan laporan bisnis yang jelas dan akurat.
- **Andal**: Stabil dan konsisten untuk mendukung kelancaran operasional harian.