'use client';

import React, { createContext, useState, useContext, useEffect, ReactNode, useCallback } from 'react';

type Theme = 'breaktime' | 'breaktime-dark';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const LOCAL_STORAGE_THEME_KEY = 'theme';

export function ThemeProvider({ children }: { children: ReactNode }) {
  const [theme, setTheme] = useState<Theme>(() => {
    if (typeof window === 'undefined') {
      return 'breaktime'; // Default server-side/initial
    }
    const storedTheme = localStorage.getItem(LOCAL_STORAGE_THEME_KEY);
    // Periksa juga preferensi sistem jika tidak ada di local storage
    if (storedTheme) {
      return storedTheme as Theme;
    } else if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'breaktime-dark';
    }
    return 'breaktime'; // Default light
  });

  // Terapkan tema ke elemen HTML dan simpan ke Local Storage
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem(LOCAL_STORAGE_THEME_KEY, theme);
  }, [theme]);

  const toggleTheme = useCallback(() => {
    setTheme((prevTheme) => (prevTheme === 'breaktime' ? 'breaktime-dark' : 'breaktime'));
  }, []);

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useThemeContext() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useThemeContext must be used within a ThemeProvider');
  }
  return context;
} 