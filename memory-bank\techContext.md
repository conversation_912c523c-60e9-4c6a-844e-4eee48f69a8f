# Konteks Teknologi

*Informasi tentang teknologi yang digunakan, pengaturan pengembangan, dan batasan teknis.*

## Teknologi Utama

- **Frontend**: Next.js (~15.1.7), <PERSON><PERSON> (~19.0.0), Tailwind CSS (~3.4.1), DaisyUI (~4.12.24)
- **Backend**: Next.js API Routes (~15.1.7) (Node.js runtime)
- **Database**: Database Relasional (Tipe spesifik tidak tercantum, tapi umum digunakan dengan Prisma seperti PostgreSQL/MySQL), diakses melalui Prisma ORM (~6.6.0)
- **Bahasa Pemrograman**: TypeScript (~5)
- **Package Manager**: Bun (berdasarkan adanya `bun.lock`)
- **Deployment**: [Belum diketahui, perlu dikonfirmasi. Vercel adalah pilihan umum untuk Next.js.]

## Pengaturan Pengembangan (Setup)

1.  <PERSON><PERSON>n Bun terinstal (lihat dokumentasi Bun).
2.  <PERSON><PERSON> repositori.
3.  Jalankan `bun install` untuk menginstal dependensi.
4.  Setup variabel lingkungan (buat file `.env` berdasarkan `.env.example` jika ada).
5.  Pastikan database berjalan dan koneksi diatur dalam skema Prisma / `.env`.
6.  Jalankan `bunx prisma migrate dev` (atau `bunx prisma db push` tergantung workflow) untuk sinkronisasi skema database.
7.  (Opsional) Jalankan `bunx prisma db seed` untuk mengisi data awal.
8.  Jalankan `bun run dev` untuk memulai server pengembangan.

## Batasan Teknis

[Belum teridentifikasi secara spesifik dari file. Perlu ditambahkan jika ada.]

## Dependensi Kunci

- **UI & Styling**: `react`, `react-dom`, `next`, `tailwindcss`, `daisyui`, `react-icons`, `framer-motion`, `recharts` (charts), `sonner` / `react-hot-toast` (notifikasi)
- **Database**: `@prisma/client`, `prisma`
- **Backend/API**: `jsonwebtoken` (Auth), `bcrypt` / `bcryptjs` (Hashing password), `zod` (Validasi)
- **Utilitas**: `date-fns` (Manipulasi tanggal), `uuid` (Generate ID), `exceljs` / `xlsx` (Excel export/import), `jspdf` / `jspdf-autotable` / `react-to-print` / `html2canvas` / `html-to-image` (Fungsionalitas cetak/PDF)
- **Dev Tools**: `typescript`, `eslint`, `postcss`, `ts-node` 