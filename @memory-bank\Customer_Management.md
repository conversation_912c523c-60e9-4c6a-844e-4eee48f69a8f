# Customer Management

*Dokumen ini berisi preferensi dan kebutuhan terkait fitur manajemen pelanggan untuk aplikasi Breaktime.*

## Poin Loyalitas

- Poin loyalitas pelanggan ditampilkan di halaman Booking dan Transaksi saat pelanggan dipilih
- Halaman yang menghadap pelanggan di mana pelanggan dapat memeriksa poin loyalitas dan riwayat kunjungan dengan memasukkan nomor telepon mereka

## Tag Pelanggan

- Pelanggan baru secara otomatis menerima tag 'Baru' tanpa memengaruhi tag yang sudah ada
- Fitur auto-tag ini diprioritaskan untuk diperbaiki
- Kemampuan untuk melihat pengguna mana yang terkait dengan tag tertentu

## Ekspor dan Filter Data

- Ekspor Excel halaman pelanggan menyertakan:
  - Alamat
  - Nomor telepon
  - Data transaksi dari setiap outlet
  - Tag pelanggan
- Data pelanggan dapat difilter berdasarkan outlet tempat mereka melakukan transaksi
- Fungsionalitas filter tanggal

## Komunikasi dan Tindakan

- Tombol tindakan WhatsApp yang membuka API wa.me secara langsung di halaman pelanggan

## Penggabungan Pelanggan

- Fitur penggabungan pelanggan di mana pelanggan dengan transaksi terbanyak menjadi catatan utama

## API dan Integrasi

- Endpoint API menyertakan data riwayat transaksi saat mengambil informasi pelanggan

## Halaman CRM

- Halaman CRM khusus yang dapat memfilter data berdasarkan outlet (Makassar, Emysaelan, Setiabudi)
- Pelacakan jenis pelanggan A/B/C/D per outlet
