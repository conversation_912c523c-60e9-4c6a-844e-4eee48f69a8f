# Deployment ke Vercel

Dokumen ini berisi petunjuk untuk men-deploy aplikasi BreaktimeDash ke Vercel.

## Persiapan

1. Pastikan Anda memiliki akun Vercel dan telah login
2. Pastikan Anda memiliki database PostgreSQL yang dapat diakses dari internet (misalnya Supabase, Neon, atau Railway)

## Konfigurasi Environment Variables

Saat men-deploy ke Vercel, pastikan Anda mengatur environment variables berikut:

1. `DATABASE_URL` - URL koneksi ke database PostgreSQL
2. `JWT_SECRET` - Secret key untuk JWT authentication

## Langkah-langkah Deployment

1. Push kode ke repository Git (GitHub, GitLab, atau Bitbucket)
2. Buka dashboard Vercel dan klik "New Project"
3. Import repository Git yang berisi kode aplikasi
4. Konfigurasi project:
   - Framework Preset: Next.js
   - Root Directory: ./
   - Build Command: `prisma generate --no-engine && next build`
   - Output Directory: .next
5. Tambahkan Environment Variables yang diperlukan
6. <PERSON><PERSON> "Deploy"

## Troubleshooting

### Error 500 pada API Endpoints

Jika Anda mengalami error 500 pada API endpoints, periksa hal-hal berikut:

1. **Database Connection**: Pastikan `DATABASE_URL` dikonfigurasi dengan benar dan database dapat diakses dari Vercel
2. **Prisma Client**: Pastikan Prisma Client di-generate dengan benar selama proses build
3. **JWT Secret**: Pastikan `JWT_SECRET` dikonfigurasi dengan benar

### Cara Melihat Logs

1. Buka dashboard Vercel
2. Pilih project Anda
3. Klik tab "Deployments"
4. Pilih deployment yang ingin diperiksa
5. Klik "Functions" untuk melihat log dari serverless functions

## Catatan Penting

- Vercel menggunakan serverless functions untuk API routes, yang memiliki batasan waktu eksekusi
- Pastikan database Anda dapat menangani koneksi yang sering dibuka dan ditutup
- Pertimbangkan untuk menggunakan Prisma Data Proxy atau Prisma Accelerate untuk performa yang lebih baik di lingkungan serverless
