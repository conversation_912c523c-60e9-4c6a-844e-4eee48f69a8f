# 🧪 **Panduan Testing Komisi Terapis**

## 📋 **Skenario Testing Wajib**

### **1. Testing Komisi Dasar**
```javascript
// Test Case: Komisi layanan standar
const serviceData = [
  {
    serviceId: "service-1",
    quantity: 1,
    defaultCommission: 50000
  }
];

const expectedCommission = 50000;
const actualCommission = calculateTherapistCommission(serviceData, []);
assert(actualCommission === expectedCommission);
```

### **2. Testing Komisi Khusus Terapis**
```javascript
// Test Case: Komisi khusus terapis
const serviceData = [
  {
    serviceId: "service-1", 
    quantity: 1,
    defaultCommission: 50000
  }
];

const specialCommissions = [
  {
    serviceId: "service-1",
    commission: 75000 // Komisi khusus lebih tinggi
  }
];

const expectedCommission = 75000;
const actualCommission = calculateTherapistCommission(serviceData, specialCommissions);
assert(actualCommission === expectedCommission);
```

### **3. Testing Multiple Quantity**
```javascript
// Test Case: Layanan dengan quantity > 1
const serviceData = [
  {
    serviceId: "service-1",
    quantity: 3,
    defaultCommission: 50000
  }
];

const expectedCommission = 150000; // 50000 * 3
const actualCommission = calculateTherapistCommission(serviceData, []);
assert(actualCommission === expectedCommission);
```

### **4. Testing Multiple Services**
```javascript
// Test Case: Multiple layanan dalam satu transaksi
const serviceData = [
  {
    serviceId: "service-1",
    quantity: 1,
    defaultCommission: 50000
  },
  {
    serviceId: "service-2", 
    quantity: 2,
    defaultCommission: 30000
  }
];

const expectedCommission = 110000; // 50000 + (30000 * 2)
const actualCommission = calculateTherapistCommission(serviceData, []);
assert(actualCommission === expectedCommission);
```

### **5. Testing Konsistensi Endpoint**
```javascript
// Test Case: Konsistensi antar endpoint
async function testEndpointConsistency() {
  const therapistId = "therapist-123";
  const startDate = "2024-01-01";
  const endDate = "2024-01-31";

  // Ambil data dari endpoint transactions
  const transactionsResponse = await fetch(`/api/transactions?therapistId=${therapistId}&startDate=${startDate}&endDate=${endDate}`);
  const transactionsData = await transactionsResponse.json();

  // Ambil data dari endpoint therapists/commissions  
  const commissionsResponse = await fetch(`/api/therapists/commissions?name=TestTherapist&startDate=${startDate}&endDate=${endDate}`);
  const commissionsData = await commissionsResponse.json();

  // Ambil data dari endpoint captain-performance
  const captainResponse = await fetch(`/api/reports/captain-performance?startDate=${startDate}&endDate=${endDate}`);
  const captainData = await captainResponse.json();

  // Validasi konsistensi komisi
  const transactionCommissions = transactionsData.transactions.map(t => t.therapistCommissionEarned);
  const commissionTotals = commissionsData.commissions.map(c => c.commission);
  
  // Pastikan total komisi sama di semua endpoint
  assert(transactionCommissions.reduce((a, b) => a + b, 0) === commissionTotals.reduce((a, b) => a + b, 0));
}
```

## 🔍 **Testing Validasi Konsistensi**

### **6. Testing Validasi Tolerance**
```javascript
// Test Case: Validasi dengan tolerance
const calculated = 100000.50;
const stored = 100000.00;
const tolerance = 1.00;

const isConsistent = validateCommissionConsistency(calculated, stored, tolerance);
assert(isConsistent === true); // Dalam tolerance

const strictTolerance = 0.01;
const isStrictConsistent = validateCommissionConsistency(calculated, stored, strictTolerance);
assert(isStrictConsistent === false); // Di luar tolerance
```

## 🚨 **Testing Edge Cases**

### **7. Testing Zero Commission**
```javascript
// Test Case: Layanan dengan komisi 0
const serviceData = [
  {
    serviceId: "free-service",
    quantity: 1,
    defaultCommission: 0
  }
];

const expectedCommission = 0;
const actualCommission = calculateTherapistCommission(serviceData, []);
assert(actualCommission === expectedCommission);
```

### **8. Testing Negative Values**
```javascript
// Test Case: Handling nilai negatif (seharusnya tidak terjadi)
const serviceData = [
  {
    serviceId: "service-1",
    quantity: -1, // Invalid
    defaultCommission: 50000
  }
];

// Sistem harus handle gracefully atau throw error
try {
  const commission = calculateTherapistCommission(serviceData, []);
  // Jika tidak throw error, pastikan hasilnya masuk akal
  assert(commission >= 0);
} catch (error) {
  // Error handling yang tepat
  assert(error.message.includes("Invalid quantity"));
}
```

## 📊 **Testing Performance**

### **9. Testing Large Dataset**
```javascript
// Test Case: Performance dengan dataset besar
const largeServiceData = [];
for (let i = 0; i < 1000; i++) {
  largeServiceData.push({
    serviceId: `service-${i}`,
    quantity: Math.floor(Math.random() * 5) + 1,
    defaultCommission: Math.floor(Math.random() * 100000) + 10000
  });
}

const startTime = performance.now();
const commission = calculateTherapistCommission(largeServiceData, []);
const endTime = performance.now();

// Pastikan perhitungan selesai dalam waktu wajar (< 100ms)
assert((endTime - startTime) < 100);
assert(commission > 0);
```

## 🔄 **Testing Integration**

### **10. Testing Database Integration**
```javascript
// Test Case: Integration dengan database
async function testDatabaseIntegration() {
  // Buat transaksi test
  const testTransaction = await createTestTransaction({
    therapistId: "test-therapist",
    services: [
      { serviceId: "service-1", quantity: 2, commission: 50000 }
    ],
    discountAmount: 10000,
    additionalCharge: 5000
  });

  // Ambil data dari API
  const response = await fetch(`/api/transactions/${testTransaction.id}`);
  const data = await response.json();

  // Validasi komisi tersimpan dengan benar
  const expectedCommission = 100000; // 50000 * 2
  assert(data.therapistCommissionEarned === expectedCommission);

  // Cleanup
  await deleteTestTransaction(testTransaction.id);
}
```

## 🎯 **Checklist Testing**

- [ ] ✅ Komisi dasar dihitung dengan benar
- [ ] ✅ Komisi khusus terapis diterapkan
- [ ] ✅ Multiple quantity dihitung benar
- [ ] ✅ Multiple services dijumlahkan benar
- [ ] ✅ Konsistensi antar endpoint
- [ ] ✅ Validasi tolerance bekerja
- [ ] ✅ Edge cases ditangani
- [ ] ✅ Performance memadai
- [ ] ✅ Database integration bekerja
- [ ] ✅ Error handling tepat

## 🚀 **Menjalankan Testing**

```bash
# Unit tests
npm test commission-utils

# Integration tests  
npm test api-endpoints

# End-to-end tests
npm test e2e-commission

# Performance tests
npm test performance
```
