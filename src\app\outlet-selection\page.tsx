'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { useOutletContext } from '@/contexts/OutletContext';
import { useAuth } from '@/contexts/AuthContext';
import { FiMapPin, FiAlertTriangle, FiInfo, FiPhone, FiClock, FiLock } from 'react-icons/fi';
import Image from 'next/image';

// Tipe outlet dari API
export type Outlet = {
  id: string;
  name: string;
  address: string;
  city?: string;
  phone: string;
  operationalHours: string;
  isOpen: boolean;
  isMain: boolean;
};

// Mapping ID ke Gambar Lokal
const outletImageMap: Record<string, string> = {
  'palu-emisaelan': '/Emysaelan.jpeg', // Gambar Emysaelan dari folder public
  'palu-setiabudi': '/Setiabudi.jpeg', // Gambar Setiabudi dari folder public
  'makassar-pengayoman': '/Makassar.jpeg', // Gambar Makassar dari folder public
  default: '/Makassar.jpeg', // Default jika ID tidak cocok
};

// Animasi
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      delayChildren: 0.3,
      staggerChildren: 0.2
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: { duration: 0.5 }
  }
};

export default function OutletSelectionPage() {
  const router = useRouter();
  const { setSelectedOutletId } = useOutletContext();
  const { user } = useAuth(); // Dapatkan user dari auth context
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isInvestor, setIsInvestor] = useState<boolean>(false); // Apakah user adalah investor
  const [allowedOutlets, setAllowedOutlets] = useState<Outlet[]>([]); // Outlet yang diizinkan untuk investor

  // Cek apakah user adalah investor dan ambil outlet yang diizinkan
  useEffect(() => {
    if (user) {
      // Cek apakah user adalah investor
      const userIsInvestor = user.role === 'INVESTOR';
      setIsInvestor(userIsInvestor);

      // Jika user adalah investor, ambil outlet yang diizinkan
      if (userIsInvestor) {
        const fetchAllowedOutlets = async () => {
          try {
            const response = await fetch(`/api/users/${user.id}`);
            if (!response.ok) throw new Error('Gagal memuat data user');
            const data = await response.json();

            // Ambil outlet yang diizinkan dari response
            if (data.user && data.user.allowedOutlets) {
              setAllowedOutlets(data.user.allowedOutlets);
            }
          } catch (err) {
            console.error('Error fetching allowed outlets:', err);
          }
        };
        fetchAllowedOutlets();
      }
    }
  }, [user]);

  // Ambil semua outlet atau filter berdasarkan outlet yang diizinkan
  useEffect(() => {
    const fetchOutlets = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch('/api/outlets');

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        const data = await response.json();
        const allOutlets = data.outlets || [];

        // Jika user adalah investor, filter outlet berdasarkan outlet yang diizinkan
        if (isInvestor && allowedOutlets.length > 0) {
          const allowedOutletIds = allowedOutlets.map(outlet => outlet.id);
          const filteredOutlets = allOutlets.filter(outlet => allowedOutletIds.includes(outlet.id));
          setOutlets(filteredOutlets);
        } else {
          setOutlets(allOutlets);
        }
      } catch (err) {
        console.error("Gagal memuat data outlet:", err);
        setError("Gagal memuat data outlet. Silakan coba lagi nanti.");
        setOutlets([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOutlets();
  }, [isInvestor, allowedOutlets]);

  const handleSelectOutlet = (outletId: string) => {
    setSelectedOutletId(outletId);
    router.push('/dashboard');
  };

  // Tampilan status
  const getStatusDisplay = (isOpen: boolean) => {
    return isOpen ?
      <span className="badge badge-success badge-sm text-success-content">Buka</span> :
      <span className="badge badge-error badge-sm text-error-content">Tutup</span>;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <span className="loading loading-spinner loading-lg text-primary"></span>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 min-h-screen bg-gray-100">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-8 text-center"
      >
        <h1 className="text-2xl md:text-3xl font-bold mb-2 text-gray-800">Pilih Outlet Breaktime</h1>
        <p className="text-gray-600">Silakan pilih outlet yang ingin Anda kelola datanya.</p>

        {/* Pesan khusus untuk investor */}
        {isInvestor && allowedOutlets.length > 0 && (
          <div className="mt-4 flex items-center justify-center gap-2 text-amber-600 text-sm">
            <FiLock className="text-amber-600" />
            <span>Anda hanya dapat melihat outlet yang diizinkan ({allowedOutlets.length} outlet)</span>
          </div>
        )}
      </motion.div>

      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="alert alert-error mb-6 shadow-sm"
        >
          <FiAlertTriangle className="h-6 w-6" />
          <span>{error}</span>
        </motion.div>
      )}

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        {outlets.map((outlet) => (
          <motion.div
            key={outlet.id}
            variants={itemVariants}
            whileHover={{ y: -5, transition: { duration: 0.2 } }}
            className={`card bg-white shadow-md hover:shadow-lg transition-shadow border border-gray-200 overflow-hidden ${!outlet.isOpen ? 'opacity-70' : ''}`}
          >
            <figure className="relative h-48 bg-gray-200">
              <Image
                src={outletImageMap[outlet.id] || outletImageMap.default}
                alt={outlet.name}
                layout="fill"
                objectFit="cover"
                className="w-full"
                priority={true}
              />
              <div className="absolute top-2 right-2">
                {getStatusDisplay(outlet.isOpen)}
              </div>
              <div className="absolute top-2 left-2 flex gap-1">
                {outlet.isMain && (
                  <div className="badge badge-primary text-xs">Utama</div>
                )}
                {isInvestor && (
                  <div className="badge badge-warning text-xs flex items-center gap-1">
                    <FiLock className="text-xs" /> Akses
                  </div>
                )}
              </div>
            </figure>
            <div className="card-body p-5">
              <h2 className="card-title text-lg font-bold text-gray-800 flex items-center mb-2">
                <FiMapPin className="mr-2 text-teal-500 flex-shrink-0" />
                <span className="truncate">{outlet.name}</span>
              </h2>
              <p className="text-gray-600 text-sm mb-1">{outlet.address}</p>
              <p className="text-gray-600 text-sm mb-3">{outlet.city || 'Kota tidak tersedia'}</p>

              <div className="space-y-2 mb-4">
                <div className="flex items-center text-sm text-gray-700">
                  <FiPhone className="w-4 h-4 mr-2 text-gray-500 flex-shrink-0" />
                  {outlet.phone}
                </div>
                <div className="flex items-center text-sm text-gray-700">
                  <FiClock className="w-4 h-4 mr-2 text-gray-500 flex-shrink-0" />
                  {outlet.operationalHours}
                </div>
              </div>

              {!outlet.isOpen && (
                <div className="mt-2 flex items-center text-red-600 text-sm">
                  <FiInfo className="mr-2 flex-shrink-0" />
                  <span>Outlet tutup saat ini</span>
                </div>
              )}

              <div className="card-actions justify-end mt-2">
                <button
                  className="btn btn-primary w-full"
                  onClick={() => handleSelectOutlet(outlet.id)}
                  disabled={!outlet.isOpen}
                >
                  {!outlet.isOpen ? 'Tutup' : 'Pilih Outlet Ini'}
                </button>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
}