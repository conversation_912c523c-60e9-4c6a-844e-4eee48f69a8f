@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'Michegar';
  src: url('/fonts/Michegar.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

.font-michegar {
  font-family: 'Michegar', sans-serif;
}

/* Animasi pulse untuk gambar Kembali Semangat */
@keyframes pulse-subtle {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.03);
  }
  100% {
    transform: scale(1);
  }
}

.animate-pulse-subtle {
  animation: pulse-subtle 3s ease-in-out infinite;
}

/* Efek glow untuk gambar Kembali Semangat */
@keyframes glow {
  0% {
    filter: drop-shadow(0 0 2px rgba(30, 110, 92, 0.5));
  }
  50% {
    filter: drop-shadow(0 0 10px rgba(30, 110, 92, 0.8));
  }
  100% {
    filter: drop-shadow(0 0 2px rgba(30, 110, 92, 0.5));
  }
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}

/* Custom Pattern Background */
.bg-pattern-dot {
  background-image: radial-gradient(rgba(255, 255, 255, 0.15) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Custom Animations */
@keyframes subtle-zoom {
  0% {
    transform: scale(1.1);
  }
  50% {
    transform: scale(1.15);
  }
  100% {
    transform: scale(1.1);
  }
}

.animate-subtle-zoom {
  animation: subtle-zoom 15s ease-in-out infinite;
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.5s ease-out forwards;
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-start-rgb));
  font-family: Arial, Helvetica, sans-serif;
}

/* Landscape Mode Optimizations */
@media screen and (orientation: landscape) {
  /* Adjust padding and margins for landscape mode */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Make sure content doesn't get cut off in landscape */
  .min-h-screen {
    min-height: 100vh;
  }

  /* Adjust card layouts for landscape */
  .card {
    max-width: 100%;
  }

  /* Adjust grid layouts for landscape */
  .grid-cols-1 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  /* Adjust tables for landscape */
  .table-container {
    overflow-x: auto;
  }

  /* Adjust form layouts for landscape */
  .form-control {
    max-width: 100%;
  }
}
