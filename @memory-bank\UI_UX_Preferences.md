# UI/UX Preferences

*Dokumen ini berisi preferensi UI/UX yang diinginkan oleh pengguna untuk aplikasi Breaktime.*

## Preferensi Umum

- Halaman responsif dengan tabel yang dapat dibaca dalam tema terang (default)
- <PERSON><PERSON> yang sesuai dengan logo.png
- Memanfaatkan animasi Framer yang sudah ada
- Menggunakan dialog modal sebagai pengganti dialog konfirmasi browser (confirm())
- Logo dengan border/lingkaran latar belakang
- Animasi split text-like
- Animasi gambar berulang dengan Framer

## Format dan Tampilan Data

- Format ID booking: B000020 menggunakan field 'displayId'
- Pesan error toast yang deskriptif untuk validasi error
- <PERSON><PERSON> dropdown dengan fungsi pencarian, terutama untuk pemilihan terapis
- Filter pencarian tidak reload pada setiap ketukan tombol namun tetap mempertahankan perilaku filter sebelumnya
- Font Michegar dan animasi Split Text untuk teks 'BADAN SEGAR URUSAN LANCAR' di halaman utama
- Antarmuka kalender modern seperti pada halaman booking untuk filter tanggal, dengan konsistensi di semua komponen

## Elemen Desain Visual

- Kata 'breaktime' mengikuti skema warna logo dengan gradien coklat di bagian hero halaman utama
- Gambar latar belakang spesifik:
  - Makassar.jpeg (halaman utama)
  - Emysaelan.jpeg
  - Setiabudi.jpeg untuk halaman lokasi outlet masing-masing
- Gambar 'kembali semangat' diposisikan di sudut kanan dengan teks di tepi kanan jauh, mempertahankan ukuran saat responsif
- Baris total dalam tabel jenis pembayaran memiliki pewarnaan yang berbeda untuk membuatnya lebih menonjol secara visual
- Teks halaman utama spesifik tentang pengalaman menyegarkan dan produktif Breaktime
- Nama outlet di bagian 'Kategori Pelanggan per Outlet' hanya menampilkan nama lokasi tanpa awalan 'breaktime'
- Nama outlet ditampilkan tanpa kata 'palu' di UI

## Implementasi PWA

- Aplikasi Web Progresif menggunakan gambar yang ada di folder publik dan file icons.json
- Mendukung orientasi landscape
- Melewati langkah generasi Prisma dalam proses build
