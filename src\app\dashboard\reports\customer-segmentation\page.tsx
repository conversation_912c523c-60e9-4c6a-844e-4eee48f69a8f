'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  FiCalendar, FiFilter, FiBarChart2, FiPieChart,
  FiAlertTriangle, FiRefreshCw, FiChevronLeft,
  FiUsers, FiUserCheck,
} from 'react-icons/fi';
import { format } from 'date-fns';
import { id as idLocale } from 'date-fns/locale';
import { useOutletContext } from '@/contexts/OutletContext';
import Link from 'next/link';

// Import komponen laporan
import { CustomerSegmentationChart } from '@/components/reports';

// Format tanggal untuk tampilan
const formatDateDisplay = (date: Date | null): string => {
  if (!date) return '';
  return format(date, 'dd MMMM yyyy', { locale: idLocale });
};

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0
  }).format(value);
};

// Varian Animasi
const fadeInUp = { hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0, transition: { duration: 0.5 } } };
const staggerContainer = { hidden: { opacity: 0 }, visible: { opacity: 1, transition: { staggerChildren: 0.1 } } };

// Interface untuk data segmentasi pelanggan
interface CustomerSegment {
  id: string;
  name: string;
  color: string;
  description: string;
}

// Kembalikan interface CustomerData ke versi asli
interface CustomerData {
  customerId: string;
  customerName: string;
  customerPhone: string;
  registeredAt: string;
  visitCount: number;
  totalSpent: number;
  avgSpent: number;
  isActive: boolean;
  segment: string;
}

// Interface untuk data yang dikirim ke Chart
interface CustomerChartData {
  id: string; // Asumsi chart butuh id (dari customerId)
  name: string; // Asumsi chart butuh name (dari customerName)
  visitCount: number;
  totalSpent: number;
  avgSpent: number;
  segment: string;
}

interface CustomerSegmentationData {
  segments: Record<string, CustomerSegment>;
  customers: CustomerData[]; // Gunakan interface asli
  segmentCounts: Record<string, number>;
  segmentSpending: Record<string, number>;
  marketingRecommendations: Record<string, string>;
  avgSpentThreshold: number;
}

interface Outlet {
  id: string;
  name: string;
}

export default function CustomerSegmentationPage() {
  const { selectedOutletId: contextOutletId } = useOutletContext() ?? { selectedOutletId: null };
  const [customerSegmentationData, setCustomerSegmentationData] = useState<CustomerSegmentationData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [chartType, setChartType] = useState<'scatter' | 'pie'>('scatter');
  const [selectedSegment, setSelectedSegment] = useState<string | null>(null);

  // Filters State
  const [startDate, setStartDate] = useState<Date | null>(() => {
    const today = new Date();
    const threeMonthsAgo = new Date(today);
    threeMonthsAgo.setMonth(today.getMonth() - 3);
    return threeMonthsAgo;
  });
  const [endDate, setEndDate] = useState<Date | null>(new Date()); // Hari ini
  const [selectedOutletId, setSelectedOutletId] = useState<string>(contextOutletId || '');

  // Load outlets for filter dropdown
  useEffect(() => {
    const fetchOutlets = async () => {
      try {
        const response = await fetch('/api/outlets?simple=true');
        if (!response.ok) throw new Error('Gagal memuat outlet');
        const data = await response.json();
        setOutlets(data.outlets || []);

        if (selectedOutletId === '' && contextOutletId && data.outlets.some((o: Outlet) => o.id === contextOutletId)) {
          setSelectedOutletId(contextOutletId);
        }
      } catch /*(err)*/ {
        // Abaikan error fetching outlets
        // console.error("Failed to fetch outlets:", err); // Optional: log error
      }
    };
    fetchOutlets();
  }, [contextOutletId, selectedOutletId]);

  // Fetch customer segmentation data
  const fetchCustomerSegmentationData = React.useCallback(async () => {
    if (!startDate || !endDate) {
      setError("Tanggal mulai dan tanggal selesai harus dipilih.");
      setCustomerSegmentationData(null);
      return;
    }

    setIsLoading(true);
    setError(null);
    setCustomerSegmentationData(null);

    // Format tanggal untuk API (YYYY-MM-DD)
    const formatLocalDate = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    const formattedStartDate = formatLocalDate(startDate);
    const formattedEndDate = formatLocalDate(endDate);

    const params = new URLSearchParams({
      startDate: formattedStartDate,
      endDate: formattedEndDate,
    });

    if (selectedOutletId) {
      params.set('outletId', selectedOutletId);
    }

    const apiUrl = `/api/reports/customer-segmentation?${params.toString()}`;

    try {
      const response = await fetch(apiUrl);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Gagal memuat data segmentasi pelanggan: ${response.statusText}`);
      }

      const data = await response.json();
      setCustomerSegmentationData(data.customerSegmentation);
    } catch (err) {
      console.error('Error fetching customer segmentation data:', err);
      setError(err instanceof Error ? err.message : "Terjadi kesalahan saat memuat data segmentasi pelanggan.");
    } finally {
      setIsLoading(false);
    }
  }, [startDate, endDate, selectedOutletId]);

  // Automatically fetch data when filters change
  useEffect(() => {
    fetchCustomerSegmentationData();
  }, [startDate, endDate, selectedOutletId, fetchCustomerSegmentationData]);

  // Filter customers by segment
  const filteredCustomers = customerSegmentationData?.customers.filter(
    customer => !selectedSegment || customer.segment === selectedSegment
  ) || [];

  // Transform data untuk dikirim ke Chart
  const customerChartData: CustomerChartData[] = useMemo(() => {
    if (!customerSegmentationData?.customers) return [];
    // Ubah nama field sesuai kebutuhan Chart (CustomerChartData)
    return customerSegmentationData.customers.map(c => ({
      id: c.customerId,
      name: c.customerName,
      visitCount: c.visitCount,
      totalSpent: c.totalSpent,
      avgSpent: c.avgSpent,
      segment: c.segment,
    }));
  }, [customerSegmentationData]);

  return (
    <motion.div
      className="container mx-auto p-4 md:p-6 space-y-6"
      initial="hidden"
      animate="visible"
      variants={staggerContainer}
    >
      {/* Header with Back Button */}
      <motion.div variants={fadeInUp} className="flex flex-col sm:flex-row items-start sm:items-center gap-2">
        <Link href="/dashboard/reports" className="btn btn-sm btn-ghost">
          <FiChevronLeft /> Kembali
        </Link>
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-800">Analisis Segmentasi Pelanggan</h1>
          <p className="text-sm sm:text-base text-gray-600">Segmentasi berdasarkan frekuensi kunjungan dan nilai transaksi</p>
        </div>
      </motion.div>

      {/* Filters Section */}
      <motion.div variants={fadeInUp} className="bg-white p-3 sm:p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 items-end">
          {/* Tanggal Mulai Dropdown Calendar */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiCalendar className="text-gray-600" /> Tanggal Mulai
            </label>
            <div className="dropdown dropdown-bottom w-full">
              <div tabIndex={0} role="button" className="btn btn-sm btn-outline w-full justify-between overflow-hidden">
                <span className="truncate">{startDate ? formatDateDisplay(startDate) : 'Pilih tanggal'}</span>
                <span className="text-xs">▼</span>
              </div>
              <div tabIndex={0} className="dropdown-content z-[10] card card-compact shadow bg-base-100 rounded-box w-64">
                <div className="card-body p-2">
                  <div className="w-full max-w-[300px] mx-auto">
                    <div className="flex justify-between items-center py-2 mb-2">
                      <button className="btn btn-sm btn-ghost" onClick={() => setStartDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() - 1, 1) : null)} aria-label="Bulan sebelumnya">«</button>
                      <div className="text-sm font-medium">
                        {startDate ? format(startDate, 'MMMM yyyy', { locale: idLocale }) : 'Pilih tanggal'}
                      </div>
                      <button className="btn btn-sm btn-ghost" onClick={() => setStartDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() + 1, 1) : null)} aria-label="Bulan berikutnya">»</button>
                    </div>
                    <div className="grid grid-cols-7 gap-1">
                      {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                        <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                      ))}
                      {startDate && Array.from({ length: 42 }, (_, i) => {
                        const firstDayOfMonth = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
                        const startingDayOfWeek = firstDayOfMonth.getDay();
                        const day = i - startingDayOfWeek + 1;
                        const date = new Date(startDate.getFullYear(), startDate.getMonth(), day);
                        const isCurrentMonth = date.getMonth() === startDate.getMonth();
                        const isSelected = startDate && date.getDate() === startDate.getDate() && date.getMonth() === startDate.getMonth() && date.getFullYear() === startDate.getFullYear();
                        const isToday = new Date().toDateString() === date.toDateString();
                        const isDisabled = !isCurrentMonth || !!(endDate && date > endDate);

                        return (
                          <button
                            key={i}
                            className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all ${isCurrentMonth ? '' : 'text-gray-300'}
                                      ${isSelected ? 'bg-primary text-primary-content' : ''}
                                      ${isToday && !isSelected ? 'border border-primary text-primary' : ''}
                                      ${isCurrentMonth && !isSelected && !isDisabled ? 'hover:bg-base-200' : ''}
                                      ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                            onClick={() => !isDisabled && setStartDate(new Date(date))}
                            disabled={isDisabled}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Tanggal Selesai Dropdown Calendar */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiCalendar className="text-gray-600" /> Tanggal Selesai
            </label>
            <div className="dropdown dropdown-bottom w-full">
              <div tabIndex={0} role="button" className="btn btn-sm btn-outline w-full justify-between overflow-hidden">
                <span className="truncate">{endDate ? formatDateDisplay(endDate) : 'Pilih tanggal'}</span>
                <span className="text-xs">▼</span>
              </div>
              <div tabIndex={0} className="dropdown-content z-[10] card card-compact shadow bg-base-100 rounded-box w-64">
                <div className="card-body p-2">
                  <div className="w-full max-w-[300px] mx-auto">
                    <div className="flex justify-between items-center py-2 mb-2">
                      <button className="btn btn-sm btn-ghost" onClick={() => setEndDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() - 1, 1) : null)} aria-label="Bulan sebelumnya">«</button>
                      <div className="text-sm font-medium">
                        {endDate ? format(endDate, 'MMMM yyyy', { locale: idLocale }) : 'Pilih tanggal'}
                      </div>
                      <button className="btn btn-sm btn-ghost" onClick={() => setEndDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() + 1, 1) : null)} aria-label="Bulan berikutnya">»</button>
                    </div>
                    <div className="grid grid-cols-7 gap-1">
                      {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                        <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                      ))}
                      {endDate && Array.from({ length: 42 }, (_, i) => {
                        const firstDayOfMonth = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
                        const startingDayOfWeek = firstDayOfMonth.getDay();
                        const day = i - startingDayOfWeek + 1;
                        const date = new Date(endDate.getFullYear(), endDate.getMonth(), day);
                        const isCurrentMonth = date.getMonth() === endDate.getMonth();
                        const isSelected = endDate && date.getDate() === endDate.getDate() && date.getMonth() === endDate.getMonth() && date.getFullYear() === endDate.getFullYear();
                        const isToday = new Date().toDateString() === date.toDateString();
                        const isDisabled = !isCurrentMonth || !!(startDate && date < startDate) || date > new Date();

                        return (
                          <button
                            key={i}
                            className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all ${isCurrentMonth ? '' : 'text-gray-300'}
                                      ${isSelected ? 'bg-primary text-primary-content' : ''}
                                      ${isToday && !isSelected ? 'border border-primary text-primary' : ''}
                                      ${isCurrentMonth && !isSelected && !isDisabled ? 'hover:bg-base-200' : ''}
                                      ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                            onClick={() => !isDisabled && setEndDate(new Date(date))}
                            disabled={isDisabled}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Filter Outlet */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiFilter className="text-gray-600" /> Outlet
            </label>
            <select
              className="select select-bordered select-sm w-full"
              value={selectedOutletId}
              onChange={(e) => setSelectedOutletId(e.target.value)}
              aria-label="Outlet Filter"
            >
              <option value="">Semua Outlet</option>
              {outlets.map(outlet => (
                <option key={outlet.id} value={outlet.id}>{outlet.name}</option>
              ))}
            </select>
          </div>

          {/* Filter Chart/Segment */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiBarChart2 className="text-gray-600" /> Visualisasi / Segmen
            </label>
            <div className="flex gap-2 items-center">
              <div className="join">
                <button
                  className={`btn btn-xs join-item ${chartType === 'scatter' ? 'btn-active' : ''}`}
                  onClick={() => setChartType('scatter')}
                  title="Scatter Chart"
                >
                  <FiBarChart2 />
                </button>
                <button
                  className={`btn btn-xs join-item ${chartType === 'pie' ? 'btn-active' : ''}`}
                  onClick={() => setChartType('pie')}
                  title="Pie Chart"
                >
                  <FiPieChart />
                </button>
              </div>
              <select
                className="select select-bordered select-sm flex-1"
                value={selectedSegment || ''}
                onChange={(e) => setSelectedSegment(e.target.value || null)}
                aria-label="Pilih Segmen Pelanggan"
              >
                <option value="">Semua Segmen</option>
                {customerSegmentationData && Object.values(customerSegmentationData.segments).map(segment => (
                  <option key={segment.id} value={segment.id}>{segment.name}</option>
                ))}
              </select>
              <button
                className="btn btn-sm btn-square btn-outline"
                onClick={fetchCustomerSegmentationData}
                title="Refresh Data"
                disabled={isLoading}
              >
                <FiRefreshCw className={isLoading ? 'animate-spin' : ''}/>
              </button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Loading State */}
      {isLoading && (
        <motion.div variants={fadeInUp} className="text-center py-10">
          <span className="loading loading-spinner loading-lg text-primary"></span>
          <p className="mt-2 text-gray-600">Memuat data segmentasi pelanggan...</p>
        </motion.div>
      )}

      {/* Error State */}
      {error && !isLoading && (
        <motion.div variants={fadeInUp} className="alert alert-error">
          <FiAlertTriangle />
          <span>Error: {error}</span>
        </motion.div>
      )}

      {/* Customer Segmentation Data Display */}
      {!isLoading && !error && customerSegmentationData && (
        <>
          {/* Segment Summary */}
          <motion.div variants={fadeInUp} className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
            {Object.values(customerSegmentationData.segments).map(segment => (
              <div
                key={segment.id}
                className={`bg-white p-3 sm:p-4 rounded-lg shadow border-l-4`}
                style={{ borderLeftColor: segment.color }}
              >
                <h3 className="font-semibold text-gray-800 text-sm sm:text-base">{segment.name}</h3>
                <p className="text-xs text-gray-500 mt-1">{segment.description}</p>
                <div className="mt-2 sm:mt-3 flex justify-between items-center">
                  <div className="text-xl sm:text-2xl font-bold">{customerSegmentationData.segmentCounts[segment.id] || 0}</div>
                  <div className="text-xs text-gray-500">pelanggan</div>
                </div>
                <div className="mt-1 text-xs text-gray-600">
                  Total: {formatCurrency(customerSegmentationData.segmentSpending[segment.id] || 0)}
                </div>
              </div>
            ))}
          </motion.div>

          {/* Chart */}
          <motion.div variants={fadeInUp} className="bg-white p-3 sm:p-4 rounded-lg shadow">
            <h2 className="text-base sm:text-lg font-semibold mb-2 sm:mb-4 flex items-center gap-2 text-gray-700">
              <FiBarChart2 /> Visualisasi Segmentasi Pelanggan
            </h2>
            <div className="w-full overflow-x-auto">
              <div className="min-w-[300px]">
                <CustomerSegmentationChart
                  customers={customerChartData}
                  segments={Object.values(customerSegmentationData.segments)}
                  type={chartType}
                  height={350}
                />
              </div>
            </div>
          </motion.div>

          {/* Marketing Recommendations */}
          <motion.div variants={fadeInUp} className="bg-white p-3 sm:p-4 rounded-lg shadow">
            <h2 className="text-base sm:text-lg font-semibold mb-2 sm:mb-4 flex items-center gap-2 text-gray-700">
              <FiUserCheck /> Rekomendasi Strategi Pemasaran
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {Object.entries(customerSegmentationData.marketingRecommendations).map(([segmentId, recommendation]) => {
                const segment = customerSegmentationData.segments[segmentId];
                if (!segment) return null;

                return (
                  <div
                    key={segmentId}
                    className={`border rounded-lg p-3 sm:p-4 border-l-4`}
                    style={{ borderLeftColor: segment.color }}
                  >
                    <div className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: segment.color }}
                      ></div>
                      <h3 className="font-semibold text-sm sm:text-base">{segment.name}</h3>
                    </div>
                    <p className="mt-2 text-xs sm:text-sm text-gray-700">{recommendation}</p>
                    <div className="mt-2 sm:mt-3 flex justify-between text-xs sm:text-sm">
                      <div className="text-gray-500">
                        {customerSegmentationData.segmentCounts[segmentId] || 0} pelanggan
                      </div>
                      <div className="text-gray-500">
                        {formatCurrency(customerSegmentationData.segmentSpending[segmentId] || 0)}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </motion.div>

          {/* Customer List */}
          <motion.div variants={fadeInUp} className="bg-white p-3 sm:p-4 rounded-lg shadow">
            <h2 className="text-base sm:text-lg font-semibold mb-2 sm:mb-4 flex items-center gap-2 text-gray-700">
              <FiUsers /> Daftar Pelanggan {selectedSegment ? `- ${customerSegmentationData.segments[selectedSegment]?.name}` : ''}
            </h2>
            <div className="overflow-x-auto">
              <div className="min-w-[600px]">
                <table className="table table-zebra w-full text-xs sm:text-sm">
                  <thead>
                    <tr>
                      <th>Nama</th>
                      <th>Telepon</th>
                      <th>Segmen</th>
                      <th className="text-right">Kunjungan</th>
                      <th className="text-right">Total</th>
                      <th className="text-right">Rata-rata</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredCustomers.slice(0, 50).map((customer) => {
                       // Cari data customer asli untuk ambil phone jika perlu
                       const originalCustomerData = customerSegmentationData.customers.find(c => c.customerId === customer.customerId);
                       return (
                         <tr key={customer.customerId}>
                           <td className="max-w-[150px] truncate">{customer.customerName}</td>
                           <td>{originalCustomerData?.customerPhone || '-'}</td>
                           <td>
                             <div className="flex items-center gap-1 sm:gap-2">
                               <div
                                 className="w-2 h-2 sm:w-3 sm:h-3 rounded-full"
                                 style={{ backgroundColor: customerSegmentationData.segments[customer.segment]?.color || '#ccc' }}
                               ></div>
                               <span className="truncate">{customerSegmentationData.segments[customer.segment]?.name || 'Unknown'}</span>
                             </div>
                           </td>
                           <td className="text-right">{customer.visitCount}</td>
                           <td className="text-right">{formatCurrency(customer.totalSpent)}</td>
                           <td className="text-right">{formatCurrency(customer.avgSpent)}</td>
                         </tr>
                       );
                    })}
                  </tbody>
                </table>
                {filteredCustomers.length > 50 && (
                  <div className="text-center text-gray-500 text-xs sm:text-sm mt-4">
                    Menampilkan 50 dari {filteredCustomers.length} pelanggan
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </>
      )}

      {/* No Data State */}
      {!isLoading && !error && (!customerSegmentationData || customerSegmentationData.customers.length === 0) && (
        <motion.div variants={fadeInUp} className="bg-white p-4 sm:p-8 rounded-lg shadow text-center">
          <FiUsers className="mx-auto text-gray-400 mb-3 sm:mb-4" size={36} />
          <h3 className="text-base sm:text-lg font-medium text-gray-900">Tidak Ada Data Pelanggan</h3>
          <p className="mt-1 sm:mt-2 text-xs sm:text-sm text-gray-500">Tidak ada data pelanggan untuk periode yang dipilih.</p>
        </motion.div>
      )}
    </motion.div>
  );
}
