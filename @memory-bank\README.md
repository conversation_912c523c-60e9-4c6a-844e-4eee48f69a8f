# Memory Bank Breaktime

*Folder ini berisi dokumentasi dan preferensi untuk aplikasi Breaktime Dashboard.*

## Tujuan

Folder @memory-bank berisi dokumentasi komprehensif tentang aplikasi Breaktime Dashboard, termasuk konteks produk, konteks teknologi, pola sistem, preferensi UI/UX, dan kebutuhan fitur. Dokumentasi ini berfungsi sebagai referensi untuk pengembangan dan pemeliharaan aplikasi.

## Struktur Folder

- **README.md**: Do<PERSON><PERSON> ini, menjelaskan tujuan dan struktur folder @memory-bank.
- **activeContext.md**: Fokus kerja saat ini, keputusan terbaru, dan langkah selanjutnya.
- **Booking_Transactions.md**: Preferensi dan kebutuhan terkait fitur booking dan transaksi.
- **Customer_Management.md**: Preferensi dan kebutuhan terkait fitur manajemen pelanggan.
- **Export_Development.md**: Preferensi dan kebutuhan terkait fitur ekspor dan pengembangan.
- **productContext.md**: Konteks produk, menjelaskan 'mengapa' di balik aplikasi Breaktime.
- **progress.md**: Status terkini proyek, apa yang sudah selesai, apa yang tersisa, dan isu yang diketahui.
- **projectbrief.md**: Ringkasan proyek, tujuan utama, lingkup, pengguna target, dan kebutuhan utama.
- **Reports_Analytics.md**: Preferensi dan kebutuhan terkait fitur laporan dan analitik.
- **Services_Therapists.md**: Preferensi dan kebutuhan terkait fitur layanan dan terapis.
- **systemPatterns.md**: Arsitektur, keputusan teknis utama, dan pola desain yang digunakan.
- **techContext.md**: Informasi tentang teknologi yang digunakan, pengaturan pengembangan, dan batasan teknis.
- **UI_UX_Preferences.md**: Preferensi UI/UX yang diinginkan oleh pengguna.
- **User_Management.md**: Preferensi dan kebutuhan terkait fitur manajemen pengguna.

## Penggunaan

Gunakan dokumentasi ini sebagai referensi saat mengembangkan fitur baru atau memperbaiki fitur yang ada. Pastikan untuk memperbarui dokumentasi ini saat ada perubahan signifikan pada aplikasi atau preferensi pengguna.
