# Progres Proyek

*Catatan tentang status terkini proyek: apa yang sudah selesai, apa yang tersisa, dan isu yang diketahui.*

## Yang Sudah Berfungsi

- Autentikasi pengguna (Login/Logout/Me)
- CRUD untuk entitas utama (Bookings, Customers, Outlets, Services, Therapists, Transactions, Users) via API routes
- Halaman dashboard dan navigasi antar modul
- Halaman login & pemilihan outlet
- Fitur poin loyalitas pelanggan
- Sistem komisi terapis
- Manajemen izin pengguna
- Pembayaran split
- Cetak struk transaksi
- Format ID booking (B000020)
- Animasi Framer di beberapa komponen UI

## Yang Perlu Disempurnakan

- Fitur Analytics & Reports dengan prioritas tinggi
- Fitur Auto-tag pelanggan baru
- Implementasi PWA dengan dukungan orientasi landscape
- Halaman CRM dengan filter outlet dan analisis pelanggan
- Fitur penggabungan pelanggan
- Ekspor Excel di halaman riwayat transaksi dan halaman pelanggan
- Visualisasi jam sibuk berdasarkan gender
- Fitur manajemen kapten untuk terapis
- Tampilan nama kapten di tabel terapis
- Halaman yang menghadap pelanggan untuk cek poin loyalitas

## Status Saat Ini

Sebagian besar fitur inti sudah berfungsi, dengan fokus pengembangan saat ini pada fitur Analytics & Reports, penyempurnaan UI/UX, dan implementasi PWA. Beberapa fitur baru seperti manajemen kapten dan penggabungan pelanggan sedang dalam pengembangan.

## Isu yang Diketahui (Known Issues)

- Perbedaan perilaku aplikasi antara mode pengembangan dan deployment Vercel
- Fitur auto-tag untuk pelanggan baru tidak berfungsi dengan benar
- Filter pencarian yang me-reload pada setiap ketukan tombol
- Beberapa tampilan tidak konsisten di berbagai perangkat
- Dropdown pemilihan kapten menampilkan semua jenis pengguna, bukan hanya staf