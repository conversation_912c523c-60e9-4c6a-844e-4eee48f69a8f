'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FiCalendar, FiFilter, FiBarChart2, FiPieChart,
  FiAlertTriangle, FiRefreshCw, FiChevronLeft,
  FiDollarSign, FiTrendingUp, FiTrendingDown
} from 'react-icons/fi';
import { format } from 'date-fns';
import { id as idLocale } from 'date-fns/locale';
import { useOutletContext } from '@/contexts/OutletContext';
import Link from 'next/link';

// Import komponen laporan
import { ServicePerformanceChart } from '@/components/reports';

// Format tanggal untuk tampilan
const formatDateDisplay = (date: Date | null): string => {
  if (!date) return '';
  return format(date, 'dd MMMM yyyy', { locale: idLocale });
};

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('id-ID', { 
    style: 'currency', 
    currency: 'IDR', 
    minimumFractionDigits: 0 
  }).format(value);
};

// Varian Animasi
const fadeInUp = { hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0, transition: { duration: 0.5 } } };
const staggerContainer = { hidden: { opacity: 0 }, visible: { opacity: 1, transition: { staggerChildren: 0.1 } } };

// Interface untuk data performa layanan
interface ServicePerformanceData {
  serviceId: string;
  serviceName: string;
  currentPrice: number;
  commission: number;
  usageCount: number;
  totalRevenue: number;
  profit: number;
  margin: number;
  trend: number;
  priceRecommendation: number;
  recommendationReason: string;
}

interface Outlet {
  id: string;
  name: string;
}

// Tipe untuk metrik chart
type ChartMetric = 'revenue' | 'profit' | 'margin' | 'count';

export default function ServicePerformancePage() {
  const { selectedOutletId: contextOutletId } = useOutletContext() ?? { selectedOutletId: null };
  const [servicePerformanceData, setServicePerformanceData] = useState<ServicePerformanceData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [chartType, setChartType] = useState<'bar' | 'pie'>('bar');
  const [chartMetric, setChartMetric] = useState<ChartMetric>('revenue');

  // Filters State
  const [startDate, setStartDate] = useState<Date | null>(() => {
    const today = new Date();
    return new Date(today.getFullYear(), today.getMonth(), 1); // Tanggal 1 bulan ini
  });
  const [endDate, setEndDate] = useState<Date | null>(new Date()); // Hari ini
  const [selectedOutletId, setSelectedOutletId] = useState<string>(contextOutletId || '');

  // Load outlets for filter dropdown
  useEffect(() => {
    const fetchOutlets = async () => {
      try {
        const response = await fetch('/api/outlets?simple=true');
        if (!response.ok) throw new Error('Gagal memuat outlet');
        const data = await response.json();
        setOutlets(data.outlets || []);

        if (selectedOutletId === '' && contextOutletId && data.outlets.some((o: Outlet) => o.id === contextOutletId)) {
          setSelectedOutletId(contextOutletId);
        }
      } catch /*(err)*/ {
        // Abaikan error fetching outlets
      }
    };
    fetchOutlets();
  }, [contextOutletId]);

  // Fetch service performance data
  const fetchServicePerformanceData = async () => {
    if (!startDate || !endDate) {
      setError("Tanggal mulai dan tanggal selesai harus dipilih.");
      setServicePerformanceData([]);
      return;
    }

    setIsLoading(true);
    setError(null);
    setServicePerformanceData([]);

    // Format tanggal untuk API (YYYY-MM-DD)
    const formatLocalDate = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    const formattedStartDate = formatLocalDate(startDate);
    const formattedEndDate = formatLocalDate(endDate);

    const params = new URLSearchParams({
      startDate: formattedStartDate,
      endDate: formattedEndDate,
    });

    if (selectedOutletId) {
      params.set('outletId', selectedOutletId);
    }

    const apiUrl = `/api/reports/service-performance?${params.toString()}`;

    try {
      const response = await fetch(apiUrl);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Gagal memuat data performa layanan: ${response.statusText}`);
      }

      const data = await response.json();
      setServicePerformanceData(data.servicePerformance);
    } catch (err) {
      console.error('Error fetching service performance data:', err);
      setError(err instanceof Error ? err.message : "Terjadi kesalahan saat memuat data performa layanan.");
    } finally {
      setIsLoading(false);
    }
  };

  // Automatically fetch data when filters change
  useEffect(() => {
    fetchServicePerformanceData();
  }, [startDate, endDate, selectedOutletId]);

  // Prepare chart data
  const chartData = servicePerformanceData.map(service => ({
    name: service.serviceName,
    count: service.usageCount,
    revenue: service.totalRevenue,
    profit: service.profit,
    margin: service.margin,
  })).slice(0, 10); // Limit to top 10 services

  return (
    <motion.div
      className="container mx-auto p-4 md:p-6 space-y-6"
      initial="hidden"
      animate="visible"
      variants={staggerContainer}
    >
      {/* Header with Back Button */}
      <motion.div variants={fadeInUp} className="flex items-center gap-2">
        <Link href="/dashboard/reports" className="btn btn-sm btn-ghost">
          <FiChevronLeft /> Kembali
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Laporan Performa Layanan</h1>
          <p className="text-gray-600">Analisis margin keuntungan dan tren popularitas layanan</p>
        </div>
      </motion.div>

      {/* Filters Section */}
      <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
          {/* Tanggal Mulai Dropdown Calendar */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiCalendar className="text-gray-600" /> Tanggal Mulai
            </label>
            <div className="dropdown dropdown-bottom w-full">
              <div tabIndex={0} role="button" className="btn btn-sm btn-outline w-full justify-between overflow-hidden">
                <span className="truncate">{startDate ? formatDateDisplay(startDate) : 'Pilih tanggal'}</span>
                <span className="text-xs">▼</span>
              </div>
              <div tabIndex={0} className="dropdown-content z-[10] card card-compact shadow bg-base-100 rounded-box w-64">
                <div className="card-body p-2">
                  <div className="w-full max-w-[300px] mx-auto">
                    <div className="flex justify-between items-center py-2 mb-2">
                      <button className="btn btn-sm btn-ghost" onClick={() => setStartDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() - 1, 1) : null)} aria-label="Bulan sebelumnya">«</button>
                      <div className="text-sm font-medium">
                        {startDate ? format(startDate, 'MMMM yyyy', { locale: idLocale }) : 'Pilih tanggal'}
                      </div>
                      <button className="btn btn-sm btn-ghost" onClick={() => setStartDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() + 1, 1) : null)} aria-label="Bulan berikutnya">»</button>
                    </div>
                    <div className="grid grid-cols-7 gap-1">
                      {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                        <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                      ))}
                      {startDate && Array.from({ length: 42 }, (_, i) => {
                        const firstDayOfMonth = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
                        const startingDayOfWeek = firstDayOfMonth.getDay();
                        const day = i - startingDayOfWeek + 1;
                        const date = new Date(startDate.getFullYear(), startDate.getMonth(), day);
                        const isCurrentMonth = date.getMonth() === startDate.getMonth();
                        const isSelected = startDate && date.getDate() === startDate.getDate() && date.getMonth() === startDate.getMonth() && date.getFullYear() === startDate.getFullYear();
                        const isToday = new Date().toDateString() === date.toDateString();
                        const isDisabled = !isCurrentMonth || !!(endDate && date > endDate);

                        return (
                          <button
                            key={i}
                            className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all ${isCurrentMonth ? '' : 'text-gray-300'}
                                      ${isSelected ? 'bg-primary text-primary-content' : ''}
                                      ${isToday && !isSelected ? 'border border-primary text-primary' : ''}
                                      ${isCurrentMonth && !isSelected && !isDisabled ? 'hover:bg-base-200' : ''}
                                      ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                            onClick={() => !isDisabled && setStartDate(new Date(date))}
                            disabled={isDisabled}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Tanggal Selesai Dropdown Calendar */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiCalendar className="text-gray-600" /> Tanggal Selesai
            </label>
            <div className="dropdown dropdown-bottom w-full">
              <div tabIndex={0} role="button" className="btn btn-sm btn-outline w-full justify-between overflow-hidden">
                <span className="truncate">{endDate ? formatDateDisplay(endDate) : 'Pilih tanggal'}</span>
                <span className="text-xs">▼</span>
              </div>
              <div tabIndex={0} className="dropdown-content z-[10] card card-compact shadow bg-base-100 rounded-box w-64">
                <div className="card-body p-2">
                  <div className="w-full max-w-[300px] mx-auto">
                    <div className="flex justify-between items-center py-2 mb-2">
                      <button className="btn btn-sm btn-ghost" onClick={() => setEndDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() - 1, 1) : null)} aria-label="Bulan sebelumnya">«</button>
                      <div className="text-sm font-medium">
                        {endDate ? format(endDate, 'MMMM yyyy', { locale: idLocale }) : 'Pilih tanggal'}
                      </div>
                      <button className="btn btn-sm btn-ghost" onClick={() => setEndDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() + 1, 1) : null)} aria-label="Bulan berikutnya">»</button>
                    </div>
                    <div className="grid grid-cols-7 gap-1">
                      {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                        <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                      ))}
                      {endDate && Array.from({ length: 42 }, (_, i) => {
                        const firstDayOfMonth = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
                        const startingDayOfWeek = firstDayOfMonth.getDay();
                        const day = i - startingDayOfWeek + 1;
                        const date = new Date(endDate.getFullYear(), endDate.getMonth(), day);
                        const isCurrentMonth = date.getMonth() === endDate.getMonth();
                        const isSelected = endDate && date.getDate() === endDate.getDate() && date.getMonth() === endDate.getMonth() && date.getFullYear() === endDate.getFullYear();
                        const isToday = new Date().toDateString() === date.toDateString();
                        const isDisabled = !isCurrentMonth || !!(startDate && date < startDate) || date > new Date();

                        return (
                          <button
                            key={i}
                            className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all ${isCurrentMonth ? '' : 'text-gray-300'}
                                      ${isSelected ? 'bg-primary text-primary-content' : ''}
                                      ${isToday && !isSelected ? 'border border-primary text-primary' : ''}
                                      ${isCurrentMonth && !isSelected && !isDisabled ? 'hover:bg-base-200' : ''}
                                      ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                            onClick={() => !isDisabled && setEndDate(new Date(date))}
                            disabled={isDisabled}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Filter Outlet */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiFilter className="text-gray-600" /> Outlet
            </label>
            <select
              className="select select-bordered select-sm w-full"
              value={selectedOutletId}
              onChange={(e) => setSelectedOutletId(e.target.value)}
              aria-label="Outlet Filter"
            >
              <option value="">Semua Outlet</option>
              {outlets.map(outlet => (
                <option key={outlet.id} value={outlet.id}>{outlet.name}</option>
              ))}
            </select>
          </div>

          {/* Chart Controls */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiBarChart2 className="text-gray-600" /> Visualisasi
            </label>
            <div className="flex gap-2 items-center">
              <div className="join">
                <button
                  className={`btn btn-xs join-item ${chartType === 'bar' ? 'btn-active' : ''}`}
                  onClick={() => setChartType('bar')}
                  title="Bar Chart"
                >
                  <FiBarChart2 />
                </button>
                <button
                  className={`btn btn-xs join-item ${chartType === 'pie' ? 'btn-active' : ''}`}
                  onClick={() => setChartType('pie')}
                  title="Pie Chart"
                >
                  <FiPieChart />
                </button>
              </div>
              <select
                className="select select-bordered select-sm flex-1"
                value={chartMetric}
                onChange={(e) => setChartMetric(e.target.value as ChartMetric)}
                aria-label="Pilih Metrik Chart"
              >
                <option value="revenue">Pendapatan</option>
                <option value="profit">Keuntungan</option>
                <option value="margin">Margin</option>
                <option value="count">Jumlah Penggunaan</option>
              </select>
              <button
                className="btn btn-sm btn-square btn-outline"
                onClick={fetchServicePerformanceData}
                title="Refresh Data"
                disabled={isLoading}
              >
                <FiRefreshCw className={isLoading ? 'animate-spin' : ''} />
              </button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Loading State */}
      {isLoading && (
        <motion.div variants={fadeInUp} className="text-center py-10">
          <span className="loading loading-spinner loading-lg text-primary"></span>
          <p className="mt-2 text-gray-600">Memuat data performa layanan...</p>
        </motion.div>
      )}

      {/* Error State */}
      {error && !isLoading && (
        <motion.div variants={fadeInUp} className="alert alert-error">
          <FiAlertTriangle />
          <span>Error: {error}</span>
        </motion.div>
      )}

      {/* Service Performance Data Display */}
      {!isLoading && !error && servicePerformanceData.length > 0 && (
        <>
          {/* Chart */}
          <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-700">
              <FiBarChart2 /> Visualisasi Performa Layanan
            </h2>
            <ServicePerformanceChart
              data={chartData}
              type={chartType}
              dataKey={chartMetric}
              height={400}
            />
          </motion.div>

          {/* Service Performance Table */}
          <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-700">
              <FiBarChart2 /> Detail Performa Layanan
            </h2>
            <div className="overflow-x-auto">
              <table className="table table-zebra w-full">
                <thead>
                  <tr>
                    <th>Layanan</th>
                    <th className="text-right">Harga</th>
                    <th className="text-right">Penggunaan</th>
                    <th className="text-right">Pendapatan</th>
                    <th className="text-right">Keuntungan</th>
                    <th className="text-right">Margin</th>
                    <th className="text-right">Tren</th>
                    <th className="text-right">Rekomendasi Harga</th>
                  </tr>
                </thead>
                <tbody>
                  {servicePerformanceData.map((service) => (
                    <tr key={service.serviceId}>
                      <td>{service.serviceName}</td>
                      <td className="text-right">{formatCurrency(service.currentPrice)}</td>
                      <td className="text-right">{service.usageCount}</td>
                      <td className="text-right">{formatCurrency(service.totalRevenue)}</td>
                      <td className="text-right">{formatCurrency(service.profit)}</td>
                      <td className="text-right">{service.margin.toFixed(1)}%</td>
                      <td className="text-right">
                        <span className={`flex items-center justify-end ${service.trend > 0 ? 'text-green-600' : service.trend < 0 ? 'text-red-600' : 'text-gray-500'}`}>
                          {service.trend > 0 ? <FiTrendingUp className="mr-1" /> : service.trend < 0 ? <FiTrendingDown className="mr-1" /> : null}
                          {service.trend.toFixed(1)}%
                        </span>
                      </td>
                      <td className="text-right">
                        <div className="flex items-center justify-end">
                          {formatCurrency(service.priceRecommendation)}
                          <div className="dropdown dropdown-left">
                            <div tabIndex={0} role="button" className="btn btn-xs btn-ghost btn-circle ml-1">
                              <span className="text-xs">?</span>
                            </div>
                            <div tabIndex={0} className="dropdown-content z-[1] card card-compact shadow bg-base-100 rounded-box w-64">
                              <div className="card-body p-2">
                                <p className="text-xs">{service.recommendationReason}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </motion.div>

          {/* Price Recommendations */}
          <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-700">
              <FiDollarSign /> Rekomendasi Harga
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {servicePerformanceData
                .filter(service => service.recommendationReason && service.recommendationReason !== 'Harga saat ini sudah optimal berdasarkan margin dan permintaan.')
                .slice(0, 4)
                .map(service => (
                  <div key={service.serviceId} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold">{service.serviceName}</h3>
                        <div className="flex items-center mt-1">
                          <span className="text-gray-600 mr-2">Harga Saat Ini:</span>
                          <span>{formatCurrency(service.currentPrice)}</span>
                        </div>
                        <div className="flex items-center mt-1">
                          <span className="text-gray-600 mr-2">Rekomendasi:</span>
                          <span className="font-medium">{formatCurrency(service.priceRecommendation)}</span>
                        </div>
                      </div>
                      <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                        service.priceRecommendation > service.currentPrice 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {service.priceRecommendation > service.currentPrice 
                          ? `+${((service.priceRecommendation - service.currentPrice) / service.currentPrice * 100).toFixed(1)}%` 
                          : `${((service.priceRecommendation - service.currentPrice) / service.currentPrice * 100).toFixed(1)}%`}
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mt-2">{service.recommendationReason}</p>
                    <div className="mt-3 grid grid-cols-3 gap-2 text-xs">
                      <div className="bg-gray-100 p-2 rounded">
                        <div className="text-gray-500">Penggunaan</div>
                        <div className="font-medium">{service.usageCount}</div>
                      </div>
                      <div className="bg-gray-100 p-2 rounded">
                        <div className="text-gray-500">Margin</div>
                        <div className="font-medium">{service.margin.toFixed(1)}%</div>
                      </div>
                      <div className="bg-gray-100 p-2 rounded">
                        <div className="text-gray-500">Tren</div>
                        <div className={`font-medium flex items-center ${service.trend > 0 ? 'text-green-600' : service.trend < 0 ? 'text-red-600' : ''}`}>
                          {service.trend > 0 ? <FiTrendingUp className="mr-1" size={10} /> : service.trend < 0 ? <FiTrendingDown className="mr-1" size={10} /> : null}
                          {service.trend.toFixed(1)}%
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </motion.div>
        </>
      )}

      {/* No Data State */}
      {!isLoading && !error && servicePerformanceData.length === 0 && (
        <motion.div variants={fadeInUp} className="bg-white p-8 rounded-lg shadow text-center">
          <FiBarChart2 className="mx-auto text-gray-400 mb-4" size={48} />
          <h3 className="text-lg font-medium text-gray-900">Tidak Ada Data Performa Layanan</h3>
          <p className="mt-2 text-gray-500">Tidak ada data performa layanan untuk periode yang dipilih.</p>
        </motion.div>
      )}
    </motion.div>
  );
}
