# Cursor AI with Memory Bank

This document explains how to use Cursor AI with the Memory Bank system for the Breaktime Dashboard project.

## Overview

Cursor AI is configured to work with the Memory Bank system, which stores important project information, preferences, and decisions. This integration ensures that Cursor AI can provide consistent and contextually relevant assistance throughout the development process.

## How to Use

1. **Accessing Memories**: When you need information about project preferences, past decisions, or requirements, ask Cursor AI about it. Cursor AI will refer to the Memory Bank to provide accurate information.

2. **Creating New Memories**: When you want to remember something important for future reference, tell Cursor AI to remember it. For example:
   ```
   Remember that we prefer to use modal dialogs instead of browser confirm() dialogs
   ```

3. **Updating Memories**: If you need to update existing information, simply tell Cursor AI about the change, and it will update the relevant memory.

## Memory Bank Structure

The Memory Bank is organized into several categories:

- **UI/UX Preferences**: Design preferences, UI components, animations, etc.
- **Visual Design Elements**: Specific visual design choices and requirements
- **PWA Implementation**: Requirements for the Progressive Web App implementation
- **Reports & Analytics**: Preferences for data visualization and reporting features
- **Customer Management**: Requirements for customer-related features
- **Booking & Transactions**: Requirements for booking and transaction features
- **Services & Therapists**: Requirements for service and therapist management
- **User Management**: Requirements for user roles, permissions, and management
- **Export & Development**: Preferences for export features and development workflow
- **General Preferences**: Other general project preferences

## Files

- **CursorAI_Role.md**: Defines the role and behavior of Cursor AI
- **Memories.md**: Contains all the memories from previous interactions
- **CursorAI_README.md**: This file, explaining how to use Cursor AI with Memory Bank

## Best Practices

1. **Be Specific**: When asking Cursor AI to remember something, be specific and concise
2. **Categorize Information**: Mention the category when creating new memories (e.g., "Remember for UI/UX that...")
3. **Verify Information**: If you're unsure about a memory, ask Cursor AI to check the Memory Bank
4. **Update Regularly**: Keep the Memory Bank updated as project requirements evolve

## Limitations

- Cursor AI can only access information that has been explicitly stored in the Memory Bank
- The Memory Bank is most effective for long-term, important information, not for temporary details
- While Cursor AI will try to maintain consistency with the Memory Bank, it may occasionally need clarification on ambiguous or conflicting information

## Support

If you encounter any issues with Cursor AI or the Memory Bank system, please contact the development team for assistance.
