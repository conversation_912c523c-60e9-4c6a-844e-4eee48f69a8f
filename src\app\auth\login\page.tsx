'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FiUser, FiLock, FiAlertTriangle } from 'react-icons/fi';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

// Definisikan varian animasi
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

export default function LoginPage() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();
  const { login, isAuthenticated, authError } = useAuth();

  // Menampilkan error dari context jika ada
  useEffect(() => {
    if (authError) {
      setError(authError);
    }
  }, [authError]);

  // Redirect jika sudah login
  useEffect(() => {
    if (isAuthenticated) {
      // Cek jika ada query parameter redirect
      const urlParams = new URLSearchParams(window.location.search);
      const redirectUrl = urlParams.get('redirect');

      // Redirect ke URL yang ditentukan jika ada, jika tidak ke outlet-selection
      router.push(redirectUrl ? decodeURIComponent(redirectUrl) : '/outlet-selection');
    }
  }, [isAuthenticated, router]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!username || !password) {
      setError('Username dan Password harus diisi.');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      console.log('Submitting login form...');

      await login(username, password);

      console.log('Login berhasil, menunggu redirect...');
      // Redirect akan ditangani oleh useEffect di atas
      // ketika isAuthenticated berubah menjadi true
    } catch (err: unknown) {
      const errorMessage = err instanceof Error
        ? err.message
        : 'Terjadi kesalahan saat login. Silakan coba lagi.';

      setError(errorMessage);
      console.error('Login error pada halaman login:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <motion.div
      data-theme="breaktime"
      className="min-h-screen flex items-center justify-center bg-gray-50 p-4 relative overflow-hidden"
      initial="hidden"
      animate="visible"
      variants={staggerContainer}
    >
      <div className="container mx-auto max-w-4xl px-4 flex items-center justify-center relative">
      {/* Bubble Background Effect */}
      <motion.div
        className="absolute w-64 h-64 bg-teal-400/30 rounded-full blur-2xl opacity-60 -z-10"
        animate={{
          y: ["100%", "-100%"], // Naik dari bawah ke atas
          x: [0, 30, -30, 0], // Goyang sedikit
          scale: [0.9, 1.1, 0.9],
        }}
        transition={{ duration: 40, repeat: Infinity, ease: "linear", repeatType: "loop" }}
      />
      <motion.div
        className="absolute w-80 h-80 bg-amber-400/30 rounded-full blur-2xl opacity-60 bottom-[-20%] right-[10%] -z-10"
        animate={{
          y: ["100%", "-150%"], // Naik lebih cepat
          x: [0, -40, 40, 0],
          scale: [1, 0.9, 1.1, 1],
        }}
        transition={{ duration: 35, repeat: Infinity, ease: "linear", repeatType: "loop", delay: 7 }}
      />
       <motion.div
        className="absolute w-52 h-52 bg-teal-400/20 rounded-full blur-2xl opacity-70 top-[80%] left-[15%] -z-10"
        animate={{
          y: ["100%", "-120%"],
          x: [0, 20, -20, 0],
          scale: [1.1, 0.9, 1.1],
        }}
        transition={{ duration: 45, repeat: Infinity, ease: "linear", repeatType: "loop", delay: 15 }}
      />
       {/* Tambah bubble kecil */}
       <motion.div
        className="absolute w-32 h-32 bg-amber-400/20 rounded-full blur-xl opacity-50 bottom-[-10%] left-[60%] -z-10"
        animate={{
          y: ["100%", "-130%"],
          x: [0, -25, 25, 0],
          scale: [0.8, 1, 0.8],
        }}
        transition={{ duration: 50, repeat: Infinity, ease: "linear", repeatType: "loop", delay: 3 }}
      />

      {/* Login Card */}
      <motion.div
        className="card w-full max-w-sm sm:max-w-md bg-white shadow-xl border border-gray-200 overflow-hidden z-10"
        variants={fadeInUp}
      >
        <figure className="px-10 pt-10">
          <motion.div variants={fadeInUp}>
            <Image
              src="/logo.png"
              alt="Breaktime Logo"
              width={80}
              height={80}
            />
          </motion.div>
        </figure>
        <div className="card-body items-center text-center">
          <motion.h2
            className="card-title text-2xl font-bold mb-6 text-gray-800"
            variants={fadeInUp}
          >
            Login Akun Anda
          </motion.h2>

          {/* Error Message */}
          {error && (
            <motion.div
              className="alert alert-error text-sm text-left mb-4 shadow-sm"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <FiAlertTriangle className="h-5 w-5" />
              <span>{error}</span>
            </motion.div>
          )}

          <motion.form
            onSubmit={handleSubmit}
            className="w-full space-y-4"
            variants={staggerContainer}
          >
            {/* Username Input */}
            <motion.div className="form-control w-full" variants={fadeInUp}>
              <label className="label">
                <span className="label-text font-medium text-gray-700">Username</span>
              </label>
              <div className="relative">
                <FiUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Masukkan username Anda"
                  className="input input-bordered w-full pl-10 bg-white text-gray-900 border-gray-300 focus:border-teal-500 focus:ring-1 focus:ring-teal-500"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  disabled={isLoading}
                  required
                />
              </div>
            </motion.div>

            {/* Password Input */}
            <motion.div className="form-control w-full" variants={fadeInUp}>
              <label className="label">
                <span className="label-text font-medium text-gray-700">Password</span>
              </label>
              <div className="relative">
                <FiLock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="password"
                  placeholder="Masukkan password Anda"
                  className="input input-bordered w-full pl-10 bg-white text-gray-900 border-gray-300 focus:border-teal-500 focus:ring-1 focus:ring-teal-500"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={isLoading}
                  required
                />
              </div>
              <label className="label">
                <Link href="#" className="label-text-alt link link-hover text-teal-600 text-sm">
                  Lupa password?
                </Link>
              </label>
            </motion.div>

            {/* Submit Button */}
            <motion.div className="form-control mt-6" variants={fadeInUp}>
              <button
                type="submit"
                className={`btn bg-teal-500 hover:bg-teal-600 text-white border-none ${isLoading ? 'loading' : ''}`}
                disabled={isLoading}
              >
                {isLoading ? 'Loading...' : 'Login'}
              </button>
            </motion.div>
          </motion.form>

          {/* Link to Register */}
          <motion.div className="mt-6 text-center" variants={fadeInUp}>
            <p className="text-sm text-gray-600">
              Belum punya akun? {' '}
              <Link href="/auth/register" className="link text-teal-600 hover:text-teal-700 font-medium">
                Daftar di sini
              </Link>
            </p>
          </motion.div>
        </div>
      </motion.div>
      </div>
    </motion.div>
  );
}