'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useOutletContext } from '@/contexts/OutletContext';
import { toast } from 'sonner';
import PermissionGuard from '@/components/permission/PermissionGuard';
import {
  FiPlus, FiSearch, FiTrash2, FiAward,
  FiTag, FiList, FiX, FiInbox, FiPhone, FiCalendar, FiUser, FiInfo,
  FiActivity, FiSettings, FiFileText, FiMessageCircle, FiUsers, FiCheck
} from 'react-icons/fi';
import { parseISO, isValid, format } from 'date-fns';
import { id as idLocale } from 'date-fns/locale/id';

// --- Interfaces (Customer diperbarui, Transaction tetap sama) ---
interface Customer {
  id: string;
  name: string;
  phone: string;
  points: number;      // Poin loyalitas
  tags: string[];      // Tag pelanggan (misal: VIP, Loyal, Baru)
  registeredAt: string | Date; // <-- <PERSON>akan registeredAt
  address?: string;    // Al<PERSON>t pelanggan (opsional)
}

interface TransactionReceiptData { // Perbarui interface ini
  outletName?: string; // Buat opsional jika mungkin tidak ada
  transactionId: string;
  createdAt: string | Date; // <-- Tambah createdAt
  customerName?: string; // Opsional
  customers?: { id: string; name: string; phone: string }[];
  therapistName?: string; // Opsional
  cashierName?: string; // Opsional
  items?: { name: string; price: number; quantity: number }[]; // Opsional
  subtotal?: number; // Opsional
  discountType?: 'percentage' | 'fixed' | 'none'; // Opsional
  discountValue?: number; // Opsional
  discountAmount?: number; // Opsional
  additionalCharge?: number; // Opsional
  tax?: number; // Opsional
  totalAmount?: number; // <-- Tambah totalAmount
  paymentMethod?: string; // Opsional
  note?: string; // Opsional
  therapistCommissionEarned?: number; // Tambahkan jika sudah ada
  // Hapus: dateTime, total
}

// Mock Data Awal (jika localStorage kosong)
// const initialMockCustomers: Omit<Customer, 'id' | 'createdAt'>[] = [ ... ];

// Kunci localStorage
// const CUSTOMER_DATA_KEY = 'customerData';
const TRANSACTION_HISTORY_KEY = 'transactionHistory';

// Varian Animasi
const fadeInUp = { hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0, transition: { duration: 0.5 } } };
const staggerContainer = { hidden: { opacity: 0 }, visible: { opacity: 1, transition: { staggerChildren: 0.1 } } };

// Helper warna badge untuk tag pelanggan
const getStatusBadgeStyle = (status: string): string => {
  const lowerStatus = status.toLowerCase();

  // Mapping tag ke style badge
  if (lowerStatus === 'a') return 'badge badge-primary'; // Tag A: Warna biru (pelanggan rutin)
  if (lowerStatus === 'b') return 'badge badge-secondary'; // Tag B: Warna ungu (kembali dalam 31-60 hari)
  if (lowerStatus === 'c') return 'badge badge-accent'; // Tag C: Warna teal/hijau kebiruan (kembali dalam 61-180 hari)
  if (lowerStatus === 'd') return 'badge badge-warning'; // Tag D: Warna kuning (kembali setelah >180 hari)
  if (lowerStatus === 'baru') return 'badge badge-info'; // Tag Baru: Warna biru muda

  // Tag lainnya
  if (lowerStatus === 'vip') return 'badge badge-error'; // VIP: Warna merah
  if (lowerStatus === 'loyal') return 'badge badge-success'; // Loyal: Warna hijau

  // Default untuk tag yang tidak dikenal
  return 'badge badge-ghost';
};

export default function CustomerPage() {
  const { } = useOutletContext(); // Outlet context (belum dipakai filter)
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // State Modal Detail
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [customerTransactionHistory, setCustomerTransactionHistory] = useState<TransactionReceiptData[]>([]);
  const [isHistoryLoading, setIsHistoryLoading] = useState(false);
  const [editedPoints, setEditedPoints] = useState<number | string>(0);
  const [newTagInput, setNewTagInput] = useState('');
  const [editedAddress, setEditedAddress] = useState('');

  // State Modal Tambah (Reused)
  const [showAddCustomerModal, setShowAddCustomerModal] = useState(false);
  const [newCustomerName, setNewCustomerName] = useState('');
  const [newCustomerPhone, setNewCustomerPhone] = useState('');
  const [newCustomerAddress, setNewCustomerAddress] = useState('');

  // State Modal Edit Pelanggan
  const [showEditCustomerModal, setShowEditCustomerModal] = useState(false);
  const [customerToEdit, setCustomerToEdit] = useState<Customer | null>(null);
  const [editedCustomerName, setEditedCustomerName] = useState('');
  const [editedCustomerPhone, setEditedCustomerPhone] = useState('');
  const [editedCustomerAddress, setEditedCustomerAddress] = useState('');
  const [isEditingCustomer, setIsEditingCustomer] = useState(false);

  // --> State untuk Modal Konfirmasi Hapus Pelanggan <--
  const [isDeleteCustomerModalOpen, setIsDeleteCustomerModalOpen] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState<Customer | null>(null);

  // State untuk proses auto-tagging
  const [isTaggingInProgress, setIsTaggingInProgress] = useState(false);
  const [isTagResultModalOpen, setIsTagResultModalOpen] = useState(false);
  const [tagResults, setTagResults] = useState<{
    total: number,
    processed: number,
    updated: number,
    details: {tag: string, count: number}[]
  }>({
    total: 0,
    processed: 0,
    updated: 0,
    details: []
  });

  // State untuk export Excel
  const [isExporting, setIsExporting] = useState(false);

  // State untuk pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(1);

  // State untuk fitur penggabungan pelanggan
  const [selectedCustomersToMerge, setSelectedCustomersToMerge] = useState<string[]>([]);
  const [isMergeModalOpen, setIsMergeModalOpen] = useState(false);
  const [isMerging, setIsMerging] = useState(false);

  // State untuk debounce pencarian
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  // State untuk filter tanggal
  const [startDate, setStartDate] = useState<string | null>(null);
  const [endDate, setEndDate] = useState<string | null>(null);
  const [showDateFilter, setShowDateFilter] = useState(false);
  const [filterType, setFilterType] = useState<'registration' | 'transaction'>('registration');

  // --- Fungsi Fetch Pelanggan (Diekstrak) ---
  const fetchCustomers = useCallback(async (pageNum = 1, searchQuery = '', dateStart = null, dateEnd = null, dateFilterType = 'registration') => {
    setIsLoading(true);
    try {
      // Buat URL dengan parameter pagination dan search
      let apiUrl = '/api/customers';
      const params = new URLSearchParams();
      params.append('page', pageNum.toString());
      params.append('limit', itemsPerPage.toString());
      if (searchQuery) {
        params.append('search', searchQuery);
      }

      // Tambahkan parameter filter tanggal jika ada
      if (dateStart) {
        // Format tanggal ke YYYY-MM-DD untuk konsistensi
        const formattedStartDate = dateStart;
        params.append('startDate', formattedStartDate);
        console.log(`Using startDate: ${formattedStartDate}`);
      }
      if (dateEnd) {
        // Format tanggal ke YYYY-MM-DD untuk konsistensi
        const formattedEndDate = dateEnd;
        params.append('endDate', formattedEndDate);
        console.log(`Using endDate: ${formattedEndDate}`);
      }

      // Tambahkan parameter jenis filter
      params.append('filterType', dateFilterType);

      // Tambahkan parameter includeTransactionHistory jika filter berdasarkan tanggal transaksi
      if (dateFilterType === 'transaction') {
        params.append('includeTransactionHistory', 'true');
      }

      // Buat URL lengkap
      const url = `${apiUrl}?${params.toString()}`;

      console.log(`Fetching customers with ${dateFilterType} filter: ${url}`);
      const response = await fetch(url);

      if (!response.ok) {
        // Coba dapatkan detail error dari response jika ada
        let errorDetail = `Error: ${response.status}`;
        try {
          const errorData = await response.json();
          if (errorData.error) {
            errorDetail = errorData.error;
          }
          if (errorData.details) {
            errorDetail += `: ${errorData.details}`;
          }
        } catch {
          // Jika tidak bisa parse JSON, gunakan pesan default
        }
        throw new Error(errorDetail);
      }

      const data = await response.json();

      // Jika ada data transaksi, simpan ke state
      if (data.customers && data.customers.length > 0 && dateFilterType === 'transaction') {
        // Simpan data transaksi untuk setiap pelanggan
        const customersWithTransactions = data.customers.map(customer => {
          // Jika ada transaksi, simpan ke state
          if (customer.transactions && customer.transactions.length > 0) {
            console.log(`Customer ${customer.name} has ${customer.transactions.length} transactions`);
            // Hapus data transaksi dari customer untuk mengurangi ukuran data
            const { transactions, ...customerData } = customer;
            return customerData;
          }
          return customer;
        });

        setCustomers(customersWithTransactions || []);
      } else {
        setCustomers(data.customers || []);
      }

      // Update pagination info dari API
      if (data.pagination) {
        setTotalPages(data.pagination.pageCount || 1);
      }

      console.log(`Loaded ${data.customers?.length || 0} customers (page ${pageNum}/${data.pagination?.pageCount || 1})`);
    } catch (error) {
      console.error('Error fetching customers:', error);
      setCustomers([]);
      toast.error("Gagal memuat data pelanggan.");
    } finally {
      setIsLoading(false);
    }
  }, [itemsPerPage, toast]); // Dependency: itemsPerPage dan toast, searchTerm dihandle oleh debounce

  // Load Customer Data dari API saat mount, currentPage, debouncedSearchTerm, atau filter tanggal berubah
  useEffect(() => {
    fetchCustomers(currentPage, debouncedSearchTerm, startDate, endDate, filterType);
  }, [fetchCustomers, currentPage, debouncedSearchTerm, startDate, endDate, filterType]);

  // Filter client-side untuk responsivitas saat mengetik
  const filteredCustomers = useMemo(() => {
    if (!searchTerm) return customers;

    // Jika searchTerm berbeda dengan debouncedSearchTerm, lakukan filter client-side
    if (searchTerm !== debouncedSearchTerm) {
      const lowerSearch = searchTerm.toLowerCase();
      return customers.filter(c =>
        c.name.toLowerCase().includes(lowerSearch) ||
        c.phone.includes(lowerSearch) ||
        c.id.toLowerCase().includes(lowerSearch) ||
        (c.tags ?? []).some(tag => tag.toLowerCase().includes(lowerSearch))
      );
    }

    // Jika searchTerm sama dengan debouncedSearchTerm, gunakan data dari API
    return customers;
  }, [customers, searchTerm, debouncedSearchTerm]);

  // Data yang ditampilkan (hasil filter client-side atau data dari API)
  const paginatedCustomers = useMemo(() => {
    return filteredCustomers;
  }, [filteredCustomers]);

  // Fungsi untuk menangani perubahan halaman
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
      // fetchCustomers akan dipanggil oleh useEffect
    }
  };

  // Fungsi untuk membuka WhatsApp dengan nomor telepon pelanggan
  const handleOpenWhatsApp = (phone: string) => {
    // Bersihkan nomor telepon dari karakter non-digit
    const cleanPhone = phone.replace(/\D/g, '');

    // Pastikan nomor telepon dimulai dengan kode negara
    let formattedPhone = cleanPhone;
    if (cleanPhone.startsWith('0')) {
      formattedPhone = `62${cleanPhone.substring(1)}`;
    } else if (!cleanPhone.startsWith('62')) {
      formattedPhone = `62${cleanPhone}`;
    }

    // Buka WhatsApp API (bukan wa.me)
    const whatsappUrl = `https://api.whatsapp.com/send?phone=${formattedPhone}`;
    window.open(whatsappUrl, '_blank');
  };



  // Fungsi untuk menangani perubahan pencarian
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    // Tidak langsung memanggil fetchCustomers, hanya update state searchTerm
  };

  // Debounce effect untuk pencarian - dinonaktifkan untuk pencarian manual
  // Kita hanya akan mengatur halaman ke 1 saat searchTerm berubah
  useEffect(() => {
    // Jika searchTerm kosong, kita tetap update debouncedSearchTerm
    if (searchTerm === '') {
      setDebouncedSearchTerm('');
      setCurrentPage(1);
    }
    // Jika tidak, kita hanya reset halaman tanpa mengubah debouncedSearchTerm
    // debouncedSearchTerm akan diubah saat tombol "Cari Sekarang" ditekan
  }, [searchTerm]);

  // --- Handlers Modal Detail ---
  const handleOpenDetailModal = async (customer: Customer) => {
    setSelectedCustomer(customer);
    setEditedPoints(customer.points ?? 0);
    setEditedAddress(customer.address ?? '');
    setNewTagInput('');
    setIsDetailModalOpen(true);
    // Load history untuk customer ini
    setIsHistoryLoading(true);

    try {
      // Ambil riwayat transaksi dari API berdasarkan ID pelanggan
      console.log(`Fetching transaction history for customer ID: ${customer.id}`);
      const response = await fetch(`/api/transactions?customerId=${customer.id}`);

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();
      console.log(`Received ${data.transactions?.length || 0} transactions for customer ID: ${customer.id}`);
      setCustomerTransactionHistory(data.transactions || []);
    } catch (error) {
      // Fallback ke localStorage dengan filter yang lebih akurat
      try {
        console.log('Falling back to localStorage for transaction history');
        const allHistory = JSON.parse(localStorage.getItem(TRANSACTION_HISTORY_KEY) || '[]');
        console.log(`Total transactions in localStorage: ${allHistory.length}`);

        // Filter berdasarkan ID pelanggan jika tersedia, jika tidak gunakan nama
        const customerHistory = allHistory.filter((trx: TransactionReceiptData) => {
          if (trx.customerId) {
            return trx.customerId === customer.id;
          } else {
            // Fallback ke nama jika ID tidak tersedia
            return trx.customerName?.toLowerCase() === customer.name.toLowerCase();
          }
        }).reverse(); // Urutkan dari yang terbaru

        console.log(`Found ${customerHistory.length} transactions for customer ID: ${customer.id} in localStorage`);
        setCustomerTransactionHistory(customerHistory);
      } catch (fallbackError) {
        console.error('Error loading transaction history from localStorage:', fallbackError);
        setCustomerTransactionHistory([]);
      }
    } finally {
      setIsHistoryLoading(false);
    }
  };

  const handleCloseDetailModal = () => {
    setIsDetailModalOpen(false);
    setSelectedCustomer(null);
    setCustomerTransactionHistory([]);
  };

  const handleSavePoints = async () => {
    const pointsToSave = Number(editedPoints) || 0;
    if (!selectedCustomer || pointsToSave < 0) return;

    try {
      const response = await fetch(`/api/customers/${selectedCustomer.id}/points`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ points: pointsToSave }),
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      await response.json();

      const updatedCustomers = customers.map(c =>
        c.id === selectedCustomer.id ? { ...c, points: pointsToSave } : c
      );
      setCustomers(updatedCustomers);
      toast.success('Poin berhasil diperbarui.');
    } catch (error) {
      toast.error('Gagal memperbarui poin. Silakan coba lagi.');
    }
  };

  const handleSaveAddress = async () => {
    if (!selectedCustomer) return;

    try {
      const response = await fetch(`/api/customers/${selectedCustomer.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ address: editedAddress }),
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();
      const updatedCustomer = data.customer;

      const updatedCustomers = customers.map(c =>
        c.id === selectedCustomer.id ? { ...c, address: updatedCustomer.address } : c
      );
      setCustomers(updatedCustomers);
      setSelectedCustomer({ ...selectedCustomer, address: updatedCustomer.address });
      toast.success('Alamat berhasil diperbarui.');
    } catch (error) {
      toast.error('Gagal memperbarui alamat. Silakan coba lagi.');
    }
  };

  const handleAddTag = async () => {
    if (!selectedCustomer || !newTagInput.trim()) return;
    const newTag = newTagInput.trim();
    // Cek duplikat (case insensitive) - Tambahkan fallback di sini
    if ((selectedCustomer.tags ?? []).some(tag => tag.toLowerCase() === newTag.toLowerCase())) {
      toast.error(`Tag "${newTag}" sudah ada.`);
      setNewTagInput('');
      return;
    }

    try {
      // Kirim request PUT ke API untuk update tags dengan format action dan tag
      const response = await fetch(`/api/customers/${selectedCustomer.id}/tags`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        // Body sesuai ekspektasi backend
        body: JSON.stringify({ action: 'add', tag: newTag }),
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json(); // Ambil data customer terbaru dari response
      const updatedCustomer = data.customer;

      // Update state lokal dengan data dari backend
      const updatedCustomers = customers.map(c =>
        c.id === selectedCustomer.id ? updatedCustomer : c
      );
      setCustomers(updatedCustomers);
      setSelectedCustomer(updatedCustomer); // Update state modal dengan data baru
      setNewTagInput(''); // Reset input
      toast.success(`Tag "${newTag}" berhasil ditambahkan.`);
    } catch (error) {
      toast.error('Gagal menambahkan tag. Silakan coba lagi.');
    }
  };

  const handleRemoveTag = async (tagToRemove: string) => {
    if (!selectedCustomer) return;

    try {
      // Kirim request PUT ke API untuk update tags dengan format action dan tag
      const response = await fetch(`/api/customers/${selectedCustomer.id}/tags`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        // Body sesuai ekspektasi backend
        body: JSON.stringify({ action: 'remove', tag: tagToRemove }),
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json(); // Ambil data customer terbaru dari response
      const updatedCustomer = data.customer;

      // Update state lokal dengan data dari backend
      const updatedCustomers = customers.map(c =>
        c.id === selectedCustomer.id ? updatedCustomer : c
      );
      setCustomers(updatedCustomers);
      setSelectedCustomer(updatedCustomer); // Update state modal dengan data baru
      toast.success(`Tag "${tagToRemove}" berhasil dihapus.`);
    } catch (error) {
      toast.error('Gagal menghapus tag. Silakan coba lagi.');
    }
  };
  // --- End Handlers Modal Detail ---

  // --- Handler Tambah Pelanggan (Adaptasi) ---
  const handleAddNewCustomer = async () => {
    if (!newCustomerName || !newCustomerPhone) return toast.error('Nama dan No. HP wajib diisi.');
    if (!/^[0-9]{10,}$/.test(newCustomerPhone)) return toast.error('Format No. HP tidak valid.');

    try {
      // Kirim request POST ke API
      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newCustomerName,
          phone: newCustomerPhone,
          address: newCustomerAddress, // Tambahkan alamat
          points: 0, // Default poin
          // Tag 'Baru' akan ditambahkan otomatis di API jika tidak ada tag lain
          tags: [],
        }),
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();

      // Update state lokal
      const updatedCustomers = [...customers, data.customer];
      setCustomers(updatedCustomers);
      setShowAddCustomerModal(false);
      setNewCustomerName('');
      setNewCustomerPhone('');
      setNewCustomerAddress(''); // Reset alamat
      toast.success(`Pelanggan "${data.customer.name}" berhasil ditambahkan.`);
    } catch (error) {
      toast.error('Gagal menambahkan pelanggan. Silakan coba lagi.');
    }
  };
  // --- End Handler Tambah ---

  // --- Handlers untuk Modal Hapus Pelanggan ---
  const handleOpenDeleteCustomerModal = (customer: Customer) => {
    setCustomerToDelete(customer);
    setIsDeleteCustomerModalOpen(true);
  };

  const handleCloseDeleteCustomerModal = () => {
    setCustomerToDelete(null);
    setIsDeleteCustomerModalOpen(false);
  };

  const confirmDeleteCustomer = async () => {
    if (!customerToDelete) return;

    setIsLoading(true);
    const customerId = customerToDelete.id;
    const customerName = customerToDelete.name;
    handleCloseDeleteCustomerModal(); // Tutup modal

    try {
      const response = await fetch(`/api/customers/${customerId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Gagal menghapus pelanggan');
      }

      // Refresh data pelanggan
      await fetchCustomers(currentPage, debouncedSearchTerm, startDate, endDate, filterType); // <-- Panggil fungsi fetch dengan parameter lengkap

      toast.success(`Pelanggan "${customerName}" berhasil dihapus.`);
    } catch (err: unknown) {
      toast.error(`Gagal menghapus pelanggan: ${err instanceof Error ? err.message : 'Terjadi kesalahan'}`);
    } finally {
      setIsLoading(false);
    }
  };
  // --- Akhir Handlers Modal Hapus Pelanggan ---

  // Modifikasi handleDeleteCustomer: Hanya buka modal
  const handleDeleteCustomer = (customer: Customer) => {
    handleOpenDeleteCustomerModal(customer);
  };

  // --- Handlers untuk Penggabungan Pelanggan ---
  const handleToggleSelectCustomerToMerge = (customerId: string) => {
    setSelectedCustomersToMerge(prev => {
      if (prev.includes(customerId)) {
        return prev.filter(id => id !== customerId);
      } else {
        return [...prev, customerId];
      }
    });
  };

  const handleOpenMergeModal = () => {
    if (selectedCustomersToMerge.length < 2) {
      toast.error('Pilih minimal 2 pelanggan untuk digabungkan');
      return;
    }
    setIsMergeModalOpen(true);
  };

  const handleCloseMergeModal = () => {
    setIsMergeModalOpen(false);
  };

  const handleMergeCustomers = async () => {
    if (selectedCustomersToMerge.length < 2) {
      toast.error('Pilih minimal 2 pelanggan untuk digabungkan');
      return;
    }

    setIsMerging(true);

    try {
      const response = await fetch('/api/customers/merge', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerIds: selectedCustomersToMerge,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Gagal menggabungkan pelanggan');
      }

      const data = await response.json();

      // Reset state
      setSelectedCustomersToMerge([]);
      setIsMergeModalOpen(false);

      // Refresh data pelanggan
      await fetchCustomers(currentPage, debouncedSearchTerm, startDate, endDate, filterType);

      toast.success(data.message || 'Pelanggan berhasil digabungkan');
    } catch (error) {
      console.error('Error merging customers:', error);
      toast.error(`Gagal menggabungkan pelanggan: ${error instanceof Error ? error.message : 'Terjadi kesalahan'}`);
    } finally {
      setIsMerging(false);
    }
  };

  const handleClearSelectedCustomers = () => {
    setSelectedCustomersToMerge([]);
  };
  // --- Akhir Handlers untuk Penggabungan Pelanggan ---

  // Fungsi untuk membuka modal edit pelanggan
  const handleOpenEditCustomerModal = (customer: Customer) => {
    setCustomerToEdit(customer);
    setEditedCustomerName(customer.name);
    setEditedCustomerPhone(customer.phone);
    setEditedCustomerAddress(customer.address || '');
    setShowEditCustomerModal(true);
  };

  // Fungsi untuk menutup modal edit pelanggan
  const handleCloseEditCustomerModal = () => {
    setShowEditCustomerModal(false);
    setCustomerToEdit(null);
    setEditedCustomerName('');
    setEditedCustomerPhone('');
    setEditedCustomerAddress('');
  };

  // Fungsi untuk menyimpan perubahan pelanggan
  const handleSaveEditedCustomer = async () => {
    if (!customerToEdit || !editedCustomerName || !editedCustomerPhone) {
      toast.error('Nama dan No. HP wajib diisi.');
      return;
    }

    if (!/^[0-9]{10,}$/.test(editedCustomerPhone)) {
      toast.error('Format No. HP tidak valid.');
      return;
    }

    setIsEditingCustomer(true);

    try {
      // Kirim request PUT ke API
      const response = await fetch(`/api/customers/${customerToEdit.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: editedCustomerName,
          phone: editedCustomerPhone,
          address: editedCustomerAddress || null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Error: ${response.status}`);
      }

      const data = await response.json();
      const updatedCustomer = data.customer;

      // Update state lokal
      const updatedCustomers = customers.map(c =>
        c.id === customerToEdit.id ? updatedCustomer : c
      );
      setCustomers(updatedCustomers);

      // Tutup modal
      handleCloseEditCustomerModal();
      toast.success(`Pelanggan "${updatedCustomer.name}" berhasil diperbarui.`);
    } catch (error) {
      console.error('Error updating customer:', error);
      toast.error(`Gagal memperbarui pelanggan: ${error instanceof Error ? error.message : 'Error tidak diketahui'}`);
    } finally {
      setIsEditingCustomer(false);
    }
  };

  // --- State untuk Auto-Tagging Pelanggan ---
  const [autoTagProgress, setAutoTagProgress] = useState(0);
  const [isAutoTagBatchProcessing, setIsAutoTagBatchProcessing] = useState(false);
  const [autoTagCurrentBatch, setAutoTagCurrentBatch] = useState(0);
  const [autoTagTotalBatches, setAutoTagTotalBatches] = useState(0);
  const [autoTagCumulativeResults, setAutoTagCumulativeResults] = useState<{
    total: number;
    processed: number;
    updated: number;
    details: { tag: string; count: number }[];
  }>({
    total: 0,
    processed: 0,
    updated: 0,
    details: []
  });

  // --- Fungsi Auto-Tagging Pelanggan ---
  const processAutoTagBatch = async (startIndex: number, isFirstBatch: boolean = false) => {
    try {
      console.log(`processAutoTagBatch called with startIndex=${startIndex}, isFirstBatch=${isFirstBatch}`);

      // Panggil API untuk mengatur tag otomatis dengan timeout yang lebih lama
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 1 menit timeout per batch

      const requestBody = {
        // Kriteria untuk tag A
        tagACriteria: {
          minTransactions: 2,
          maxIntervalDays: 30
        },
        // Kriteria untuk tag B, C, D
        tagBCriteria: {
          minDays: 31,
          maxDays: 60
        },
        tagCCriteria: {
          minDays: 61,
          maxDays: 180
        },
        tagDCriteria: {
          minDays: 181
        },
        startIndex: startIndex,
        processAll: false // Proses sebagian saja
      };

      console.log("Sending request to auto-tag API:", requestBody);

      const response = await fetch('/api/customers/auto-tag', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Gagal mengatur tag otomatis');
      }

      const data = await response.json();
      console.log("API response:", data);

      // Update progress
      setAutoTagProgress(data.progress || 0);

      // Gabungkan hasil dengan hasil sebelumnya
      const updatedDetails = [...autoTagCumulativeResults.details];

      // Update count untuk setiap tag
      (data.tagCounts || []).forEach((tagData: { tag: string; count: number }) => {
        const existingTagIndex = updatedDetails.findIndex(item => item.tag === tagData.tag);
        if (existingTagIndex >= 0) {
          updatedDetails[existingTagIndex].count += tagData.count;
        } else {
          updatedDetails.push(tagData);
        }
      });

      // Update hasil kumulatif
      setAutoTagCumulativeResults(prevResults => ({
        total: data.totalCustomers || 0,
        processed: prevResults.processed + (data.processedCustomers || 0),
        updated: prevResults.updated + (data.updatedCustomers || 0),
        details: updatedDetails
      }));

      // Jika belum selesai, lanjutkan ke batch berikutnya
      if (!data.isComplete) {
        setAutoTagCurrentBatch(prev => prev + 1);
        return {
          isComplete: false,
          nextStartIndex: data.nextStartIndex
        };
      }

      // Jika sudah selesai
      return {
        isComplete: true,
        nextStartIndex: 0
      };
    } catch (error) {
      console.error('Error in auto-tag batch:', error);

      // Tampilkan pesan error yang lebih informatif
      if (error instanceof DOMException && error.name === 'AbortError') {
        toast.error('Proses auto-tag batch membutuhkan waktu terlalu lama dan telah dihentikan.');
      } else {
        toast.error(`Gagal mengatur tag otomatis batch: ${error instanceof Error ? error.message : 'Terjadi kesalahan'}`);
      }

      return {
        isComplete: true, // Hentikan proses jika terjadi error
        nextStartIndex: 0,
        error: true
      };
    }
  };

  const handleAutoTagCustomers = async () => {
    setIsTaggingInProgress(true);
    setIsAutoTagBatchProcessing(true);
    setAutoTagProgress(0);
    setAutoTagCurrentBatch(1);
    setAutoTagTotalBatches(0); // Akan diupdate setelah batch pertama

    // Reset hasil kumulatif
    setAutoTagCumulativeResults({
      total: 0,
      processed: 0,
      updated: 0,
      details: []
    });

    // Tampilkan toast untuk memberi tahu pengguna bahwa proses sedang berjalan
    const loadingToast = toast.loading(
      'Sedang memproses tag pelanggan. Proses ini akan berjalan secara bertahap...',
      { duration: Infinity }
    );

    try {
      let startIndex = 0;
      let isComplete = false;
      let hasError = false;
      let batchCount = 1;

      // Proses batch pertama untuk mendapatkan total
      console.log("Starting first batch processing...");
      const firstBatchResult = await processAutoTagBatch(startIndex, true);
      console.log("First batch result:", firstBatchResult);

      if (firstBatchResult.error) {
        console.error("Error in first batch:", firstBatchResult.error);
        hasError = true;
        isComplete = true;
      } else {
        // Perkirakan total batch berdasarkan hasil batch pertama
        const estimatedTotalBatches = Math.ceil(100 / (autoTagProgress || 1));
        console.log(`Estimated total batches: ${estimatedTotalBatches} (progress: ${autoTagProgress}%)`);
        setAutoTagTotalBatches(estimatedTotalBatches);

        startIndex = firstBatchResult.nextStartIndex;
        isComplete = firstBatchResult.isComplete;

        console.log(`After first batch - nextStartIndex: ${startIndex}, isComplete: ${isComplete}`);

        // Jika tidak ada pelanggan yang diproses, tampilkan pesan
        if (autoTagCumulativeResults.processed === 0) {
          toast.info("Tidak ada pelanggan yang perlu diproses");
        }

        // Lanjutkan dengan batch berikutnya jika belum selesai
        while (!isComplete && !hasError) {
          batchCount++;
          setAutoTagCurrentBatch(batchCount);

          // Tambahkan jeda kecil antara batch untuk menghindari overload server
          await new Promise(resolve => setTimeout(resolve, 500));

          console.log(`Processing batch ${batchCount} starting at index ${startIndex}...`);
          const batchResult = await processAutoTagBatch(startIndex);
          console.log(`Batch ${batchCount} result:`, batchResult);

          if (batchResult.error) {
            console.error(`Error in batch ${batchCount}:`, batchResult.error);
            hasError = true;
            break;
          }

          startIndex = batchResult.nextStartIndex;
          isComplete = batchResult.isComplete;
          console.log(`After batch ${batchCount} - nextStartIndex: ${startIndex}, isComplete: ${isComplete}, processed so far: ${autoTagCumulativeResults.processed}`);

          // Jika tidak ada perubahan dalam nextStartIndex, berarti ada masalah
          if (startIndex === 0 && !isComplete) {
            console.warn("Warning: nextStartIndex is 0 but process is not complete. Forcing completion.");
            isComplete = true;
          }
        }
      }

      // Tutup toast loading
      toast.dismiss(loadingToast);

      if (!hasError) {
        // Simpan hasil tagging ke state utama
        setTagResults(autoTagCumulativeResults);

        // Refresh data pelanggan
        await fetchCustomers();

        // Tampilkan modal hasil
        setIsTagResultModalOpen(true);
        toast.success('Tag pelanggan berhasil diperbarui!');
      }
    } catch (error) {
      // Tutup toast loading
      toast.dismiss(loadingToast);

      console.error('Error in auto-tag process:', error);
      toast.error(`Gagal mengatur tag otomatis: ${error instanceof Error ? error.message : 'Terjadi kesalahan'}`);
    } finally {
      setIsTaggingInProgress(false);
      setIsAutoTagBatchProcessing(false);
    }
  };

  const handleCloseTagResultModal = () => {
    setIsTagResultModalOpen(false);
  };

  // --- Fungsi Download Excel ---
  const handleDownloadExcel = async () => {
    setIsExporting(true);

    // Tampilkan toast loading untuk memberi tahu pengguna bahwa proses sedang berjalan
    const loadingToast = toast.loading('Sedang mengunduh data pelanggan. Proses ini mungkin membutuhkan waktu beberapa saat...');

    try {
      // Hitung total pelanggan terlebih dahulu untuk informasi
      let countApiUrl = '/api/customers/count';
      const countParams = new URLSearchParams();

      // Tambahkan parameter filter tanggal jika ada
      if (startDate) {
        countParams.append('startDate', startDate);
      }
      if (endDate) {
        countParams.append('endDate', endDate);
      }

      // Tambahkan parameter jenis filter
      countParams.append('filterType', filterType);

      // Buat URL lengkap
      const countUrl = `${countApiUrl}?${countParams.toString()}`;

      console.log(`Counting customers with filter: ${countUrl}`);

      const countResponse = await fetch(countUrl, {
        method: 'GET',
      }).catch((error) => {
        console.error('Error counting customers:', error);
        return null;
      });

      let totalCustomers = 0;
      if (countResponse && countResponse.ok) {
        const countData = await countResponse.json();
        totalCustomers = countData.count || 0;
      }

      // Panggil API untuk mendapatkan data Excel dengan timeout yang lebih lama
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 detik timeout

      // Buat URL dengan parameter filter
      let apiUrl = '/api/customers/export-excel';
      const params = new URLSearchParams();

      // Tambahkan parameter filter tanggal jika ada
      if (startDate) {
        params.append('startDate', startDate);
      }
      if (endDate) {
        params.append('endDate', endDate);
      }

      // Tambahkan parameter jenis filter
      params.append('filterType', filterType);

      // Buat URL lengkap
      const excelUrl = `${apiUrl}?${params.toString()}`;

      console.log(`Mengunduh Excel dengan filter: ${excelUrl}`);

      const response = await fetch(excelUrl, {
        method: 'GET',
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        // Coba dapatkan detail error dari response jika ada
        let errorDetail = 'Gagal mengunduh data Excel';
        try {
          const errorData = await response.json();
          if (errorData.error) {
            errorDetail = errorData.error;
          }
          if (errorData.details) {
            errorDetail += `: ${errorData.details}`;
          }
        } catch {
          // Jika tidak bisa parse JSON, gunakan pesan default
        }
        throw new Error(errorDetail);
      }

      // Dapatkan blob dari response
      const blob = await response.blob();

      // Cek ukuran file untuk memastikan data lengkap
      const fileSizeKB = Math.round(blob.size / 1024);
      console.log(`Ukuran file Excel: ${fileSizeKB} KB`);

      if (totalCustomers > 0 && fileSizeKB < 10) {
        throw new Error('File Excel terlalu kecil, mungkin data tidak lengkap');
      }

      // Buat URL untuk download
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;

      // Dapatkan tanggal untuk nama file
      const today = new Date();
      const formattedDate = format(today, 'dd-MM-yyyy', { locale: idLocale });
      a.download = `Data_Pelanggan_${formattedDate}.xlsx`;

      // Trigger download
      document.body.appendChild(a);
      a.click();

      // Cleanup
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      // Tutup toast loading
      toast.dismiss(loadingToast);

      // Tampilkan pesan sukses dengan informasi jumlah data
      if (totalCustomers > 0) {
        toast.success(`Data pelanggan berhasil diunduh (${totalCustomers} pelanggan)`, {
          description: "File Excel berisi semua data pelanggan aktif di sistem",
          duration: 5000
        });
      } else {
        toast.success("Data pelanggan berhasil diunduh", {
          description: "File Excel berisi semua data pelanggan aktif di sistem",
          duration: 5000
        });
      }
    } catch (error) {
      // Tutup toast loading
      toast.dismiss(loadingToast);

      // Tampilkan pesan error yang lebih informatif
      if (error instanceof DOMException && error.name === 'AbortError') {
        toast.error('Proses download membutuhkan waktu terlalu lama dan telah dihentikan. Coba lagi nanti atau hubungi administrator.');
      } else {
        toast.error(`Gagal mengunduh data: ${error instanceof Error ? error.message : 'Terjadi kesalahan'}`);
      }
      console.error('Error saat download Excel:', error);
    } finally {
      setIsExporting(false);
    }
  };

  if (isLoading) {
    return <div className="flex justify-center items-center h-screen"><span className="loading loading-spinner loading-lg text-primary"></span></div>;
  }

  return (
    <motion.div
      className="w-full max-w-[1600px] mx-auto p-2 md:p-4"
      initial="hidden"
      animate="visible"
      variants={staggerContainer}
    >
      <motion.div variants={fadeInUp} className="flex flex-col md:flex-row justify-between items-start md:items-center gap-3 md:gap-0 mb-4 md:mb-6">
        <h1 className="text-2xl md:text-3xl font-semibold text-base-content">Pelanggan Saat Ini</h1>
        <div className="flex flex-wrap gap-2 w-full md:w-auto">
          <PermissionGuard module="customers" action="read">
            <button
              type="button"
              className="btn btn-success btn-xs md:btn-sm shadow-md flex-1 md:flex-none text-xs md:text-sm"
              onClick={handleDownloadExcel}
              disabled={isExporting}
            >
              {isExporting ? (
                <span className="loading loading-spinner loading-xs mr-1"></span>
              ) : (
                <FiFileText className="mr-1"/>
              )}
              <span className="hidden xs:inline">Download Excel</span>
              <span className="inline xs:hidden">Excel Data</span>
            </button>
          </PermissionGuard>
          <PermissionGuard module="customers" action="update">
            <button
              type="button"
              className="btn btn-secondary btn-xs md:btn-sm shadow-md flex-1 md:flex-none text-xs md:text-sm"
              onClick={handleAutoTagCustomers}
              disabled={isTaggingInProgress}
            >
              {isTaggingInProgress ? (
                <span className="loading loading-spinner loading-xs mr-1"></span>
              ) : (
                <FiTag className="mr-1"/>
              )}
              <span className="hidden xs:inline">Atur Tag Otomatis</span>
              <span className="inline xs:hidden">Tag Auto</span>
            </button>
          </PermissionGuard>
          <PermissionGuard module="customers" action="create">
            <button
              type="button"
              className="btn btn-primary btn-xs md:btn-sm shadow-md flex-1 md:flex-none text-xs md:text-sm"
              onClick={() => setShowAddCustomerModal(true)}
            >
              <FiPlus className="mr-1"/>
              <span className="hidden xs:inline">Tambah Pelanggan Baru</span>
              <span className="inline xs:hidden">+ Pelanggan</span>
            </button>
          </PermissionGuard>

          <PermissionGuard module="customers" action="update">
            <button
              type="button"
              className={`btn ${selectedCustomersToMerge.length >= 2 ? 'btn-accent' : 'btn-disabled'} btn-xs md:btn-sm shadow-md flex-1 md:flex-none text-xs md:text-sm`}
              onClick={handleOpenMergeModal}
              disabled={selectedCustomersToMerge.length < 2}
            >
              <FiUsers className="mr-1"/>
              <span className="hidden xs:inline">Gabungkan Pelanggan ({selectedCustomersToMerge.length})</span>
              <span className="inline xs:hidden">Gabungkan ({selectedCustomersToMerge.length})</span>
            </button>
          </PermissionGuard>
        </div>
      </motion.div>

      <motion.div variants={fadeInUp} className="mb-4 md:mb-6">
         <div className="form-control">
            <div className="flex flex-col md:flex-row gap-2 w-full">
               {/* Search Input */}
               <div className="join shadow-sm w-full md:w-2/3">
                  <input
                     type="text"
                     placeholder="Cari Nama, No. HP, ID, Tag..."
                     className="input input-xs md:input-sm join-item w-full placeholder:text-base-content/60 text-base-content text-xs md:text-sm"
                     value={searchTerm}
                     onChange={handleSearchChange}
                  />
                  <button
                     type="button"
                     onClick={() => setDebouncedSearchTerm(searchTerm)}
                     className="btn btn-ghost btn-xs md:btn-sm join-item border border-base-300"
                     aria-label="Cari"
                     title="Cari"
                  >
                     <FiSearch className="text-base-content/70"/>
                  </button>
               </div>

               {/* Date Filter Toggle Button */}
               <div className="w-full md:w-1/3 flex justify-end">
                  <button
                     type="button"
                     onClick={() => setShowDateFilter(!showDateFilter)}
                     className={`btn btn-xs md:btn-sm ${showDateFilter ? 'btn-primary' : 'btn-outline btn-primary'}`}
                  >
                     <FiCalendar className="mr-1" />
                     Filter Tanggal {startDate && endDate ? '(Aktif)' : ''}
                  </button>
               </div>
            </div>

            {/* Date Filter Controls */}
            {showDateFilter && (
               <div className="mt-3 p-3 bg-base-200/50 rounded-lg shadow-sm">
                  <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-3">
                     <div className="text-sm font-medium">Filter Berdasarkan Tanggal</div>
                     <div className="flex mt-2 md:mt-0">
                        <div className="btn-group btn-group-xs">
                           <button
                              type="button"
                              onClick={() => setFilterType('registration')}
                              className={`btn btn-xs ${filterType === 'registration' ? 'btn-active' : ''}`}
                              title="Filter berdasarkan tanggal registrasi pelanggan"
                           >
                              Registrasi
                           </button>
                           <button
                              type="button"
                              onClick={() => setFilterType('transaction')}
                              className={`btn btn-xs ${filterType === 'transaction' ? 'btn-active' : ''}`}
                              title="Filter berdasarkan tanggal transaksi pelanggan"
                           >
                              Transaksi
                           </button>
                        </div>
                     </div>
                  </div>

                  <div className="text-xs text-base-content/70 mb-3">
                     {filterType === 'registration'
                        ? 'Menampilkan pelanggan berdasarkan tanggal pendaftaran'
                        : 'Menampilkan pelanggan yang memiliki transaksi pada rentang tanggal yang dipilih'
                     }
                  </div>

                  <div className="flex flex-col md:flex-row gap-2">
                     <div className="form-control w-full">
                        <label className="label py-0">
                           <span className="label-text text-xs">Tanggal Mulai</span>
                        </label>
                        <input
                           type="date"
                           className="input input-xs md:input-sm input-bordered w-full"
                           value={startDate || ''}
                           onChange={(e) => setStartDate(e.target.value || null)}
                           title="Pilih tanggal mulai"
                           aria-label="Tanggal Mulai"
                        />
                     </div>
                     <div className="form-control w-full">
                        <label className="label py-0">
                           <span className="label-text text-xs">Tanggal Akhir</span>
                        </label>
                        <input
                           type="date"
                           className="input input-xs md:input-sm input-bordered w-full"
                           value={endDate || ''}
                           onChange={(e) => setEndDate(e.target.value || null)}
                           title="Pilih tanggal akhir"
                           aria-label="Tanggal Akhir"
                        />
                     </div>
                     <div className="form-control w-full md:w-auto flex flex-row md:flex-col justify-end md:justify-end gap-1 mt-2 md:mt-4">
                        <button
                           type="button"
                           onClick={() => {
                              setStartDate(null);
                              setEndDate(null);
                              setCurrentPage(1);
                           }}
                           className="btn btn-xs btn-ghost"
                        >
                           Reset
                        </button>
                        <button
                           type="button"
                           onClick={() => {
                              setCurrentPage(1);
                           }}
                           className="btn btn-xs btn-primary"
                           disabled={!startDate && !endDate}
                        >
                           Terapkan Filter
                        </button>
                     </div>
                  </div>
               </div>
            )}

            {/* Search Status */}
            <div className="flex justify-between items-center mt-2">
               <div className="text-xs text-base-content/70">
                  {searchTerm !== debouncedSearchTerm && searchTerm && (
                     <span className="px-2 py-0.5 bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 rounded-full text-[0.65rem] font-medium">
                        Pencarian lokal: "{searchTerm}"
                     </span>
                  )}
                  {(startDate || endDate) && (
                     <span className="px-2 py-0.5 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full text-[0.65rem] font-medium ml-1">
                        Filter {filterType === 'registration' ? 'registrasi' : 'transaksi'}: {startDate ? format(new Date(startDate), 'dd/MM/yyyy') : ''}
                        {startDate && endDate ? ' - ' : ''}
                        {endDate ? format(new Date(endDate), 'dd/MM/yyyy') : ''}
                     </span>
                  )}
               </div>
               <button
                  type="button"
                  onClick={() => setDebouncedSearchTerm(searchTerm)}
                  className={`btn btn-xs btn-primary ${searchTerm === debouncedSearchTerm ? 'btn-disabled' : ''}`}
                  disabled={searchTerm === debouncedSearchTerm}
               >
                  Cari Sekarang
               </button>
            </div>
         </div>
      </motion.div>

      <motion.div variants={fadeInUp} className="card bg-base-100 shadow-xl border border-base-300/30 overflow-hidden w-full flex-grow min-h-[calc(100vh-220px)]">
        <div className="card-body p-0 h-full flex flex-col">
          {/* Tabel untuk layar medium dan di atasnya */}
          <div className="hidden md:block overflow-x-auto flex-grow">
            <table className="table w-full text-xs md:text-sm h-full">
              <thead className="bg-base-200/50 text-base-content/70 font-medium">
                <tr>
                  <th className="p-2 md:p-3 font-semibold text-center w-10">
                    {selectedCustomersToMerge.length > 0 && (
                      <div className="tooltip" data-tip="Batalkan pilihan">
                        <button
                          type="button"
                          className="btn btn-xs btn-ghost text-error"
                          onClick={handleClearSelectedCustomers}
                        >
                          <FiX size={14} />
                        </button>
                      </div>
                    )}
                  </th>
                  <th className="p-2 md:p-3 font-semibold text-left"><FiUser className="inline-block mr-1 md:mr-2"/>Nama</th>
                  <th className="p-2 md:p-3 font-semibold text-left"><FiPhone className="inline-block mr-1 md:mr-2"/>Kontak</th>
                  <th className="p-2 md:p-3 font-semibold text-left"><FiInfo className="inline-block mr-1 md:mr-2"/>ID</th>
                  <th className="p-2 md:p-3 font-semibold text-left"><FiCalendar className="inline-block mr-1 md:mr-2"/>Tanggal</th>
                  <th className="p-2 md:p-3 font-semibold text-left"><FiActivity className="inline-block mr-1 md:mr-2"/>Tag</th>
                  <th className="p-2 md:p-3 font-semibold text-center"><FiSettings className="inline-block mr-1"/>Aksi</th>
                </tr>
              </thead>
              <tbody>
                {isLoading && (
                  <tr><td colSpan={7} className="text-center p-10"><span className="loading loading-dots loading-md text-primary"></span></td></tr>
                )}
                {!isLoading && Array.isArray(filteredCustomers) && filteredCustomers.length === 0 && (
                   <tr><td colSpan={7} className="text-center text-base-content/60 py-8 md:py-12">
                      <FiInbox className="w-10 h-10 md:w-12 md:h-12 mx-auto mb-2 md:mb-3 text-base-content/30"/>
                      {searchTerm ? `Tidak ada pelanggan cocok dengan "${searchTerm}".` : 'Belum ada data pelanggan.'}
                   </td></tr>
                )}
                {!isLoading && Array.isArray(paginatedCustomers) && paginatedCustomers.map((customer) => (
                  <tr key={customer.id} className="hover:bg-base-200/50 border-b border-base-300/30 last:border-b-0">
                    <td className="p-2 md:p-3 text-center">
                      <input
                        type="checkbox"
                        className="checkbox checkbox-sm checkbox-primary"
                        checked={selectedCustomersToMerge.includes(customer.id)}
                        onChange={() => handleToggleSelectCustomerToMerge(customer.id)}
                        aria-label={`Pilih pelanggan ${customer.name}`}
                      />
                    </td>
                    <td className="p-2 md:p-3">
                      <div className="flex items-center space-x-3">
                        <div className="avatar">
                          <div className="mask mask-squircle w-8 h-8 md:w-10 md:h-10 bg-base-300">
                             <div className="flex items-center justify-center w-full h-full text-base-content/70 font-medium">
                               {customer.name.charAt(0).toUpperCase()}
                             </div>
                          </div>
                        </div>
                        <div className="break-words">
                          <div className="font-bold text-base-content">{customer.name}</div>
                        </div>
                      </div>
                    </td>
                    <td className="p-2 md:p-3 text-base-content/80 align-middle">{customer.phone}</td>
                    <td className="p-2 md:p-3 text-base-content/80 align-middle">
                      <span className="font-mono text-[0.65rem] md:text-xs bg-base-200/50 px-1.5 py-0.5 rounded">{customer.id.substring(0, 8)}</span>
                    </td>
                    <td className="p-2 md:p-3 text-base-content/80 align-middle">
                      {(() => {
                        try {
                          const date = customer.registeredAt ? parseISO(customer.registeredAt as string) : null;
                          if (date && isValid(date)) {
                            return format(date, 'dd/MM/yy', { locale: idLocale });
                          }
                        } catch { /* Tangkap error tanpa variabel jika tidak digunakan */ }
                        return '-';
                      })()}
                    </td>
                    <td className="p-2 md:p-3 align-middle">
                      <div className="flex flex-wrap gap-1">
                        {(customer.tags ?? []).map((tag, index) => (
                           <span key={`${customer.id}-tag-table-${index}`} className={`badge badge-xs md:badge-sm rounded-full px-1.5 md:px-2.5 py-1 md:py-1.5 text-[0.65rem] md:text-xs font-semibold ${getStatusBadgeStyle(tag)}`}>{tag}</span>
                        ))}
                        {(customer.tags ?? []).length === 0 && <span className="badge badge-ghost badge-xs md:badge-sm rounded-full px-1.5 md:px-2.5 py-1 md:py-1.5 text-[0.65rem] md:text-xs font-semibold">-</span>}
                      </div>
                    </td>
                    <td className="p-2 md:p-3 text-center align-middle">
                      <div className="flex justify-center space-x-1">
                         <button
                           type="button"
                           className="btn btn-xs btn-ghost text-base-content/60 hover:text-info tooltip"
                           data-tip="Lihat Detail"
                           onClick={() => handleOpenDetailModal(customer)}
                           aria-label="Lihat Detail"
                         >
                            <FiInfo size={12} className="md:w-3.5 md:h-3.5"/>
                         </button>
                         <button
                           type="button"
                           className="btn btn-xs btn-ghost text-green-600 hover:text-green-700 tooltip"
                           data-tip="WhatsApp"
                           onClick={() => handleOpenWhatsApp(customer.phone)}
                           aria-label="Hubungi via WhatsApp"
                         >
                            <FiMessageCircle size={12} className="md:w-3.5 md:h-3.5"/>
                         </button>
                         <PermissionGuard module="customers" action="update">
                           <button
                             type="button"
                             className="btn btn-xs btn-ghost text-primary tooltip"
                             data-tip="Edit Pelanggan"
                             onClick={() => handleOpenEditCustomerModal(customer)}
                             aria-label="Edit Pelanggan"
                           >
                             <FiSettings size={12} className="md:w-3.5 md:h-3.5"/>
                           </button>
                         </PermissionGuard>
                         <PermissionGuard module="customers" action="delete">
                           <button
                             type="button"
                             className="btn btn-xs btn-ghost text-error tooltip"
                             data-tip="Hapus Pelanggan"
                             onClick={() => handleDeleteCustomer(customer)}
                             aria-label="Hapus Pelanggan"
                           >
                             <FiTrash2 size={12} className="md:w-3.5 md:h-3.5"/>
                           </button>
                         </PermissionGuard>
                       </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Tampilan kartu untuk layar kecil */}
          <div className="md:hidden flex-grow overflow-y-auto">
            {isLoading && (
              <div className="flex justify-center items-center p-10">
                <span className="loading loading-dots loading-md text-primary"></span>
              </div>
            )}
            {!isLoading && Array.isArray(filteredCustomers) && filteredCustomers.length === 0 && (
              <div className="text-center text-base-content/60 py-8">
                <FiInbox className="w-10 h-10 mx-auto mb-2 text-base-content/30"/>
                {searchTerm ? `Tidak ada pelanggan cocok dengan "${searchTerm}".` : 'Belum ada data pelanggan.'}
              </div>
            )}
            {!isLoading && Array.isArray(paginatedCustomers) && (
              <div className="grid grid-cols-1 gap-3 p-3">
                {paginatedCustomers.map((customer) => {
                  // Format tanggal
                  let formattedDate = '-';
                  try {
                    const date = customer.registeredAt ? parseISO(customer.registeredAt as string) : null;
                    if (date && isValid(date)) {
                      formattedDate = format(date, 'dd/MM/yy', { locale: idLocale });
                    }
                  } catch {}

                  return (
                    <div key={customer.id} className="card bg-base-100 shadow-sm border border-base-300/30">
                      <div className="card-body p-3">
                        <div className="flex items-start gap-3 mb-2">
                          <div className="avatar">
                            <div className="mask mask-squircle w-10 h-10 bg-base-300">
                              <div className="flex items-center justify-center w-full h-full text-base-content/70 font-medium">
                                {customer.name.charAt(0).toUpperCase()}
                              </div>
                            </div>
                          </div>
                          <div className="flex-grow">
                            <h3 className="font-bold text-base-content">{customer.name}</h3>
                            <p className="text-xs text-base-content/80">{customer.phone}</p>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-1 text-xs mb-2">
                          <div className="text-base-content/70">ID:</div>
                          <div className="text-base-content/80">
                            <span className="font-mono text-[0.65rem] bg-base-200/50 px-1.5 py-0.5 rounded">
                              {customer.id.substring(0, 8)}
                            </span>
                          </div>

                          <div className="text-base-content/70">Tanggal:</div>
                          <div className="text-base-content/80">{formattedDate}</div>
                        </div>

                        <div className="mb-3">
                          <div className="text-xs text-base-content/70 mb-1">Tag:</div>
                          <div className="flex flex-wrap gap-1">
                            {(customer.tags ?? []).map((tag, index) => (
                              <span
                                key={`${customer.id}-tag-card-${index}`}
                                className={`badge badge-xs rounded-full px-1.5 py-1 text-[0.65rem] font-semibold ${getStatusBadgeStyle(tag)}`}
                              >
                                {tag}
                              </span>
                            ))}
                            {(customer.tags ?? []).length === 0 &&
                              <span className="badge badge-ghost badge-xs rounded-full px-1.5 py-1 text-[0.65rem] font-semibold">-</span>
                            }
                          </div>
                        </div>

                        <div className="card-actions justify-end border-t border-base-200 pt-2">
                          <button
                            type="button"
                            className="btn btn-xs btn-ghost text-base-content/60 hover:text-info"
                            onClick={() => handleOpenDetailModal(customer)}
                            aria-label="Lihat Detail"
                          >
                            <FiInfo size={12} className="mr-1"/> Detail
                          </button>
                          <button
                            type="button"
                            className="btn btn-xs btn-ghost text-green-600 hover:text-green-700"
                            onClick={() => handleOpenWhatsApp(customer.phone)}
                            aria-label="Hubungi via WhatsApp"
                          >
                            <FiMessageCircle size={12} className="mr-1"/> WhatsApp
                          </button>
                          <PermissionGuard module="customers" action="update">
                            <button
                              type="button"
                              className="btn btn-xs btn-ghost text-primary"
                              onClick={() => handleOpenEditCustomerModal(customer)}
                              aria-label="Edit Pelanggan"
                            >
                              <FiSettings size={12} className="mr-1"/> Edit
                            </button>
                          </PermissionGuard>
                          <PermissionGuard module="customers" action="delete">
                            <button
                              type="button"
                              className="btn btn-xs btn-ghost text-error"
                              onClick={() => handleDeleteCustomer(customer)}
                              aria-label="Hapus Pelanggan"
                            >
                              <FiTrash2 size={12} className="mr-1"/> Hapus
                            </button>
                          </PermissionGuard>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Pagination - Modern Style */}
          {paginatedCustomers.length > 0 && (
            <div className="flex flex-col md:flex-row justify-between md:items-center py-4 px-4 border-t border-base-300/30 bg-base-100 rounded-b-lg">
              <div className="text-xs text-base-content/70 mb-3 md:mb-0 text-center md:text-left">
                <span className="inline-flex items-center gap-1.5">
                  <span className="inline-block w-2 h-2 rounded-full bg-primary animate-pulse"></span>
                  Menampilkan {paginatedCustomers.length} data
                  {searchTerm !== debouncedSearchTerm ?
                    <span className="ml-1 px-2 py-0.5 bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 rounded-full text-[0.65rem] font-medium">Filter lokal: "{searchTerm}"</span> :
                    searchTerm && <span className="ml-1 px-2 py-0.5 bg-base-200 rounded-full text-[0.65rem] font-medium">Filter: "{searchTerm}"</span>
                  }
                </span>
              </div>

              <div className="flex flex-wrap justify-center items-center gap-2">
                {/* Items per page selector - Modern Style */}
                <div className="flex items-center bg-base-200/70 rounded-full px-3 py-1 mr-1">
                  <span className="text-xs text-base-content/70 mr-2">Tampilkan:</span>
                  <select
                    className="select select-xs bg-transparent border-none focus:outline-none"
                    value={itemsPerPage}
                    title="Jumlah item per halaman"
                    aria-label="Jumlah item per halaman"
                    onChange={(e) => {
                      const newItemsPerPage = Number(e.target.value);
                      setItemsPerPage(newItemsPerPage);
                      setCurrentPage(1);
                      // fetchCustomers akan dipanggil oleh useEffect
                    }}
                  >
                    <option value="10">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                  </select>
                </div>

                {/* Desktop Pagination - Modern Style */}
                <div className="hidden md:flex items-center bg-base-200/70 rounded-full p-1 shadow-sm">
                  {/* First Page Button */}
                  <button
                    className={`w-8 h-8 flex items-center justify-center rounded-full ${currentPage === 1 ? 'text-base-content/30 cursor-not-allowed' : 'hover:bg-base-300 text-base-content/70'}`}
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1 || isLoading}
                    aria-label="First Page"
                  >
                    «
                  </button>

                  {/* Previous Page Button */}
                  <button
                    className={`w-8 h-8 flex items-center justify-center rounded-full ${currentPage === 1 ? 'text-base-content/30 cursor-not-allowed' : 'hover:bg-base-300 text-base-content/70'}`}
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1 || isLoading}
                    aria-label="Previous Page"
                  >
                    ‹
                  </button>

                  {/* Page Numbers - Modern Style */}
                  {(() => {
                    const pages = [];
                    const maxVisiblePages = 3; // Reduced for cleaner look

                    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
                    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

                    if (endPage - startPage + 1 < maxVisiblePages) {
                      startPage = Math.max(1, endPage - maxVisiblePages + 1);
                    }

                    // First page with dot separator
                    if (startPage > 1) {
                      pages.push(
                        <button
                          key="page-1"
                          className={`w-8 h-8 flex items-center justify-center rounded-full ${1 === currentPage ? 'bg-primary text-primary-content font-medium' : 'hover:bg-base-300 text-base-content/70'}`}
                          onClick={() => handlePageChange(1)}
                          disabled={isLoading}
                        >
                          1
                        </button>
                      );

                      if (startPage > 2) {
                        pages.push(
                          <span key="ellipsis-start" className="w-8 h-8 flex items-center justify-center text-base-content/50">
                            ...
                          </span>
                        );
                      }
                    }

                    // Pages in range
                    for (let i = startPage; i <= endPage; i++) {
                      pages.push(
                        <button
                          key={`page-${i}`}
                          className={`w-8 h-8 flex items-center justify-center rounded-full ${i === currentPage ? 'bg-primary text-primary-content font-medium' : 'hover:bg-base-300 text-base-content/70'}`}
                          onClick={() => handlePageChange(i)}
                          disabled={isLoading}
                        >
                          {i}
                        </button>
                      );
                    }

                    // Last page with dot separator
                    if (endPage < totalPages) {
                      if (endPage < totalPages - 1) {
                        pages.push(
                          <span key="ellipsis-end" className="w-8 h-8 flex items-center justify-center text-base-content/50">
                            ...
                          </span>
                        );
                      }

                      pages.push(
                        <button
                          key={`page-${totalPages}`}
                          className={`w-8 h-8 flex items-center justify-center rounded-full ${totalPages === currentPage ? 'bg-primary text-primary-content font-medium' : 'hover:bg-base-300 text-base-content/70'}`}
                          onClick={() => handlePageChange(totalPages)}
                          disabled={isLoading}
                        >
                          {totalPages}
                        </button>
                      );
                    }

                    return pages;
                  })()}

                  {/* Next Page Button */}
                  <button
                    className={`w-8 h-8 flex items-center justify-center rounded-full ${currentPage === totalPages ? 'text-base-content/30 cursor-not-allowed' : 'hover:bg-base-300 text-base-content/70'}`}
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages || isLoading}
                    aria-label="Next Page"
                  >
                    ›
                  </button>

                  {/* Last Page Button */}
                  <button
                    className={`w-8 h-8 flex items-center justify-center rounded-full ${currentPage === totalPages ? 'text-base-content/30 cursor-not-allowed' : 'hover:bg-base-300 text-base-content/70'}`}
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage === totalPages || isLoading}
                    aria-label="Last Page"
                  >
                    »
                  </button>
                </div>

                {/* Mobile Pagination - Modern Style */}
                <div className="md:hidden flex items-center bg-base-200/70 rounded-full p-1 shadow-sm">
                  <button
                    className={`w-8 h-8 flex items-center justify-center rounded-full ${currentPage === 1 ? 'text-base-content/30 cursor-not-allowed' : 'hover:bg-base-300 text-base-content/70'}`}
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1 || isLoading}
                  >
                    ‹
                  </button>

                  <span className="px-2 text-xs font-medium">
                    {currentPage} / {totalPages}
                  </span>

                  <button
                    className={`w-8 h-8 flex items-center justify-center rounded-full ${currentPage === totalPages ? 'text-base-content/30 cursor-not-allowed' : 'hover:bg-base-300 text-base-content/70'}`}
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages || isLoading}
                  >
                    ›
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </motion.div>

      <dialog id="customer_detail_modal" className={`modal modal-bottom sm:modal-middle ${isDetailModalOpen ? 'modal-open' : ''}`}>
        <div className="modal-box w-full max-w-5xl border border-base-300/50 shadow-lg p-4 md:p-6 lg:p-8">
          <button onClick={handleCloseDetailModal} className="btn btn-sm btn-circle btn-ghost absolute right-4 top-4 z-10 text-base-content">✕</button>
          {selectedCustomer && (
             <>
               <div className="flex flex-col sm:flex-row sm:items-center mb-6 pb-4 border-b border-base-300/50">
                 <div className="avatar mr-4 mb-3 sm:mb-0">
                    <div className="w-14 h-14 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2">
                       <span className="text-2xl font-bold flex items-center justify-center w-full h-full bg-primary text-primary-content">{selectedCustomer.name.charAt(0).toUpperCase()}</span>
                    </div>
                 </div>
                 <div className="flex-grow">
                   <h3 className="font-bold text-xl md:text-2xl text-base-content">{selectedCustomer.name}</h3>
                   <div className="text-sm text-base-content/70 flex flex-col xs:flex-row xs:items-center gap-1 xs:gap-2">
                     <div className="flex items-center">
                       <span className="mr-1">ID:</span>
                       <span className="font-mono text-xs bg-base-200/70 px-1.5 py-0.5 rounded">{selectedCustomer.id.substring(0, 8)}</span>
                     </div>
                     <span className="hidden xs:inline">|</span>
                     <div>
                       <span className="mr-1">Bergabung:</span>
                       <span>
                         {(() => {
                           try {
                             const date = selectedCustomer.registeredAt ? parseISO(selectedCustomer.registeredAt as string) : null;
                             if (date && isValid(date)) {
                               return format(date, 'dd MMM yyyy', { locale: idLocale }); // Format lebih ringkas
                             }
                           } catch { /* Tangkap error */ }
                           return '-';
                         })()}
                       </span>
                     </div>
                     <span className="hidden xs:inline">|</span>
                     <div>
                       <button
                         type="button"
                         className="btn btn-xs btn-success gap-1"
                         onClick={() => handleOpenWhatsApp(selectedCustomer.phone)}
                         aria-label="Hubungi via WhatsApp"
                       >
                         <FiMessageCircle size={12} />
                         WhatsApp {selectedCustomer.phone}
                       </button>
                     </div>
                   </div>
                 </div>
               </div>

               <div className="space-y-4 md:space-y-6">
                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                   {/* Poin Loyalitas */}
                   <div className="bg-base-200/30 border border-base-300/40 rounded-lg p-3 md:p-5 shadow-sm">
                     <h4 className="text-sm md:text-base font-semibold flex items-center mb-3 text-secondary">
                       <FiAward className="mr-2"/> Poin Loyalitas
                     </h4>
                     <div className="flex items-center gap-2">
                       <input
                         type="number"
                         className="input input-sm md:input-md input-bordered flex-grow w-full [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none text-base-content"
                         value={editedPoints}
                         onChange={(e) => {
                           setEditedPoints(e.target.value);
                         }}
                         min="0"
                       />
                       <button
                         className="btn btn-sm md:btn-md btn-secondary flex-shrink-0"
                         onClick={handleSavePoints}
                         disabled={editedPoints === '' ? selectedCustomer.points === 0 : Number(editedPoints) === selectedCustomer.points}
                       >
                          Simpan
                        </button>
                     </div>
                   </div>

                   {/* Tag Pelanggan */}
                   <div className="bg-base-200/30 border border-base-300/40 rounded-lg p-3 md:p-5 shadow-sm">
                      <h4 className="text-sm md:text-base font-semibold flex items-center mb-3 text-info">
                        <FiTag className="mr-2"/> Tag Pelanggan
                      </h4>
                      <div className="flex flex-wrap gap-2 mb-4 min-h-[2.5rem] p-2 rounded-md bg-base-100/50 border border-base-300/30">
                        {(selectedCustomer.tags ?? []).length === 0 && <span className="text-xs text-base-content/60 italic self-center px-1">Belum ada tag</span>}
                        {(selectedCustomer.tags ?? []).map((tag, index) => (
                           <div key={`modal-tag-${selectedCustomer.id}-${index}`} className={`badge badge-md md:badge-lg ${getStatusBadgeStyle(tag)} group font-semibold`}>
                              {tag}
                              <button onClick={() => handleRemoveTag(tag)} className="ml-1.5 opacity-40 group-hover:opacity-100 hover:text-error transition-opacity duration-150"><FiX size={12}/></button>
                           </div>
                        ))}
                      </div>
                      <div className="flex items-center gap-2">
                        <input
                          type="text"
                          placeholder="Tambah tag..."
                          className="input input-sm md:input-md input-bordered flex-grow w-full placeholder:text-base-content/60 text-base-content"
                          value={newTagInput}
                          onChange={(e) => setNewTagInput(e.target.value)}
                          onKeyDown={(e) => e.key === 'Enter' && handleAddTag()}
                        />
                        <button
                          className="btn btn-sm md:btn-md btn-info flex-shrink-0"
                          onClick={handleAddTag}
                          disabled={!newTagInput.trim()}
                         >
                           + Tag
                         </button>
                      </div>
                    </div>
                 </div>

                 {/* Alamat Pelanggan */}
                 <div className="bg-base-200/30 border border-base-300/40 rounded-lg p-3 md:p-5 shadow-sm">
                   <h4 className="text-sm md:text-base font-semibold flex items-center mb-3 text-primary">
                     <FiInfo className="mr-2"/> Alamat Pelanggan
                   </h4>
                   <div className="flex flex-col gap-2">
                     <textarea
                       className="textarea textarea-bordered w-full h-20 md:h-24 text-base-content text-sm md:text-base"
                       placeholder="Masukkan alamat pelanggan..."
                       value={editedAddress}
                       onChange={(e) => setEditedAddress(e.target.value)}
                     />
                     <div className="flex justify-end">
                       <button
                         className="btn btn-sm btn-primary mt-2"
                         onClick={handleSaveAddress}
                         disabled={editedAddress === (selectedCustomer.address || '')}
                       >
                         Simpan Alamat
                       </button>
                     </div>
                   </div>
                 </div>

                 {/* Riwayat Transaksi */}
                 <div className="bg-base-200/30 border border-base-300/40 rounded-lg p-3 md:p-5 shadow-sm flex flex-col">
                    <h4 className="text-sm md:text-base font-semibold flex items-center mb-3 text-accent">
                      <FiList className="mr-2"/> Riwayat Transaksi
                    </h4>
                    <div className="flex-grow overflow-y-auto -mr-2 pr-2 space-y-3 max-h-60 md:max-h-96">
                      {isHistoryLoading ? (
                         <div className="flex justify-center items-center h-40"><span className="loading loading-spinner text-accent"></span></div>
                       ) : (
                         <>
                           {console.log(`Rendering transaction history for ${selectedCustomer?.name} (ID: ${selectedCustomer?.id}). Total: ${customerTransactionHistory?.length || 0} transactions`)}
                           {Array.isArray(customerTransactionHistory) && customerTransactionHistory.length === 0 && (
                              <div className="text-center py-10 md:py-16 text-base-content/60 italic flex flex-col items-center justify-center h-full">
                                <FiInbox className="w-8 h-8 md:w-10 md:h-10 mb-2 md:mb-3"/>
                                Tidak ada riwayat transaksi.
                              </div>
                           )}
                           {Array.isArray(customerTransactionHistory) && customerTransactionHistory
                             .filter(trx => trx)
                             .map(trx => {
                               let formattedDate = '-';
                               try {
                                 if (trx.createdAt) {
                                   const dateObj = parseISO(trx.createdAt as string);
                                   if (isValid(dateObj)) {
                                     // Format tanggal yang lebih ringkas untuk mobile
                                     formattedDate = format(dateObj,
                                       window.innerWidth < 768 ? 'dd MMM yyyy - HH:mm' : 'EEEE, dd MMM yyyy - HH:mm',
                                       { locale: idLocale }
                                     );
                                   }
                                 }
                               } catch (e) {
                                 // Abaikan error parsing tanggal
                               }

                               // Verifikasi bahwa transaksi ini milik pelanggan yang dipilih
                               const isCustomerMatch = trx.customer?.id === selectedCustomer?.id ||
                                                     trx.customerId === selectedCustomer?.id ||
                                                     trx.customer?.name?.toLowerCase() === selectedCustomer?.name?.toLowerCase();

                               // Log untuk debugging
                               if (!isCustomerMatch) {
                                 console.warn(`Transaction ${trx.transactionId} customer mismatch: Expected ${selectedCustomer?.id} (${selectedCustomer?.name}), got ${trx.customer?.id || trx.customerId} (${trx.customer?.name || 'Unknown'})`);
                               }

                               // Hanya tampilkan transaksi milik pelanggan yang dipilih
                               return isCustomerMatch ? (
                                <div key={trx.transactionId} className="border-b border-base-300/50 pb-3 last:border-b-0">
                                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-1 sm:gap-0 mb-1.5">
                                    <div>
                                      <span className="font-mono text-[0.65rem] md:text-[0.7rem] text-base-content/70 bg-base-300/40 px-1.5 py-0.5 rounded">ID: {trx.transactionId}</span>
                                      <p className="text-base-content/90 text-xs md:text-sm mt-1 line-clamp-1">
                                        {(trx.items ?? []).map(i => i.name).join(', ')}
                                      </p>
                                    </div>
                                    <span className="font-semibold text-base-content text-base md:text-lg whitespace-nowrap sm:ml-2">
                                      Rp {(trx.totalAmount ?? 0).toLocaleString('id-ID')}
                                    </span>
                                  </div>
                                  <div className="text-base-content/60 text-xs mt-1 space-y-1">
                                    <p className="line-clamp-1">{formattedDate} | {trx.outletName ?? 'Outlet Tidak Diketahui'}</p>
                                    <div className="flex flex-wrap gap-x-3 md:gap-x-4">
                                      {trx.therapistName && (
                                        <p className="flex items-center text-[0.65rem] md:text-xs">
                                          <span className="font-semibold mr-1">Terapis:</span> {trx.therapistName}
                                        </p>
                                      )}
                                      {trx.cashierName && (
                                        <p className="flex items-center text-[0.65rem] md:text-xs">
                                          <span className="font-semibold mr-1">Kasir:</span> {trx.cashierName}
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                </div>
                               ) : null;
                             })
                           }
                         </>
                       )}
                     </div>
                   </div>

               </div>
                <div className="modal-action mt-6 md:mt-8 pt-4 md:pt-6 border-t border-base-300/50">
                   <button className="btn btn-sm md:btn-md btn-ghost" onClick={handleCloseDetailModal}>Tutup</button>
               </div>
             </>
          )}
        </div>
         <form method="dialog" className="modal-backdrop"> <button onClick={handleCloseDetailModal}>close</button> </form>
      </dialog>

      <dialog id="add_customer_modal" className={`modal ${showAddCustomerModal ? 'modal-open' : ''}`}>
         <div className="modal-box border border-base-300">
           <h3 className="font-bold text-lg text-base-content mb-4">Tambah Pelanggan Baru</h3>
           <div className="form-control">
             <label className="label"><span className="label-text text-base-content/80">Nama Pelanggan</span></label>
             <input type="text" placeholder="Nama Lengkap" className="input input-bordered w-full text-base-content" value={newCustomerName} onChange={(e) => setNewCustomerName(e.target.value)} />
           </div>
           <div className="form-control mt-4">
             <label className="label"><span className="label-text text-base-content/80">Nomor HP</span></label>
             <input type="tel" placeholder="08xxxx" className="input input-bordered w-full text-base-content" value={newCustomerPhone} onChange={(e) => setNewCustomerPhone(e.target.value)} />
           </div>
           <div className="form-control mt-4">
             <label className="label"><span className="label-text text-base-content/80">Alamat</span></label>
             <textarea placeholder="Alamat lengkap" className="textarea textarea-bordered w-full text-base-content" value={newCustomerAddress} onChange={(e) => setNewCustomerAddress(e.target.value)} />
           </div>
           <div className="modal-action mt-6">
              <button className="btn btn-sm" onClick={() => setShowAddCustomerModal(false)}>Batal</button>
             <button className="btn btn-primary btn-sm" onClick={handleAddNewCustomer} disabled={!newCustomerName || !newCustomerPhone}>Simpan Pelanggan</button>
           </div>
         </div>
          <form method="dialog" className="modal-backdrop"> <button onClick={() => setShowAddCustomerModal(false)}>close</button> </form>
      </dialog>

      {/* --> Modal Konfirmasi Hapus Pelanggan (Pastikan ini lengkap) <-- */}
      <dialog id="delete_customer_modal" className={`modal ${isDeleteCustomerModalOpen ? 'modal-open' : ''}`}>
        <div className="modal-box max-w-sm border border-base-300">
          <button onClick={handleCloseDeleteCustomerModal} className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2 z-10">✕</button>
          <h3 className="font-bold text-lg text-error">Konfirmasi Hapus Pelanggan</h3>
          <p className="py-4 text-base-content/80">
            Anda yakin ingin menghapus pelanggan &quot;<span className="font-semibold">{customerToDelete?.name}</span>&quot;?
            <br/> Tindakan ini tidak dapat dibatalkan dan akan menghapus data pelanggan secara permanen jika tidak ada riwayat booking/transaksi terkait.
          </p>
          <div className="modal-action">
            <button className="btn btn-sm btn-ghost" onClick={handleCloseDeleteCustomerModal} disabled={isLoading}>
              Batal
            </button>
            <button className="btn btn-sm btn-error" onClick={confirmDeleteCustomer} disabled={isLoading}>
              {isLoading && <span className="loading loading-spinner loading-xs"></span>}
              Ya, Hapus
            </button>
          </div>
        </div>
        <form method="dialog" className="modal-backdrop"> <button onClick={handleCloseDeleteCustomerModal}>close</button> </form>
      </dialog>

      {/* Modal Edit Pelanggan */}
      <dialog id="merge_customer_modal" className={`modal ${isMergeModalOpen ? 'modal-open' : ''}`}>
        <div className="modal-box max-w-md border border-base-300">
          <button
            type="button"
            onClick={handleCloseMergeModal}
            className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2 z-10"
            aria-label="Tutup"
          >✕</button>
          <h3 className="font-bold text-lg text-accent">Konfirmasi Gabungkan Pelanggan</h3>
          <p className="py-4 text-base-content/80">
            Anda akan menggabungkan <span className="font-semibold">{selectedCustomersToMerge.length}</span> pelanggan.
            <br/><br/>
            <span className="text-warning font-medium">Perhatian:</span> Pelanggan dengan transaksi terbanyak akan menjadi data utama, dan semua transaksi dari pelanggan lain akan dipindahkan ke pelanggan utama. Pelanggan lainnya akan dihapus.
            <br/><br/>
            Tindakan ini tidak dapat dibatalkan.
          </p>

          {selectedCustomersToMerge.length >= 2 && (
            <div className="bg-base-200 p-3 rounded-lg mb-4">
              <h4 className="font-medium mb-2 flex items-center">
                <FiUsers className="mr-2" /> Pelanggan yang akan digabungkan:
              </h4>
              <ul className="space-y-2 max-h-40 overflow-y-auto">
                {paginatedCustomers
                  .filter(c => selectedCustomersToMerge.includes(c.id))
                  .map(customer => (
                    <li key={`merge-${customer.id}`} className="flex items-center gap-2">
                      <div className="avatar">
                        <div className="mask mask-squircle w-6 h-6 bg-base-300">
                          <div className="flex items-center justify-center w-full h-full text-base-content/70 font-medium">
                            {customer.name.charAt(0).toUpperCase()}
                          </div>
                        </div>
                      </div>
                      <span className="font-medium">{customer.name}</span>
                      <span className="text-xs text-base-content/70">{customer.phone}</span>
                    </li>
                  ))
                }
              </ul>
            </div>
          )}

          <div className="modal-action">
            <button
              type="button"
              className="btn btn-sm btn-ghost"
              onClick={handleCloseMergeModal}
              disabled={isMerging}
            >
              Batal
            </button>
            <button
              type="button"
              className="btn btn-sm btn-accent"
              onClick={handleMergeCustomers}
              disabled={isMerging || selectedCustomersToMerge.length < 2}
            >
              {isMerging && <span className="loading loading-spinner loading-xs"></span>}
              Ya, Gabungkan
            </button>
          </div>
        </div>
        <form method="dialog" className="modal-backdrop">
          <button
            type="button"
            onClick={handleCloseMergeModal}
            aria-label="Tutup dialog"
          >close</button>
        </form>
      </dialog>
      <dialog id="edit_customer_modal" className={`modal ${showEditCustomerModal ? 'modal-open' : ''}`}>
        <div className="modal-box border border-base-300">
          <h3 className="font-bold text-lg text-base-content mb-4">Edit Pelanggan</h3>
          <div className="form-control">
            <label className="label"><span className="label-text text-base-content/80">Nama Pelanggan</span></label>
            <input
              type="text"
              placeholder="Nama Lengkap"
              className="input input-bordered w-full text-base-content"
              value={editedCustomerName}
              onChange={(e) => setEditedCustomerName(e.target.value)}
            />
          </div>
          <div className="form-control mt-4">
            <label className="label"><span className="label-text text-base-content/80">Nomor HP</span></label>
            <input
              type="tel"
              placeholder="08xxxx"
              className="input input-bordered w-full text-base-content"
              value={editedCustomerPhone}
              onChange={(e) => setEditedCustomerPhone(e.target.value)}
            />
          </div>
          <div className="form-control mt-4">
            <label className="label"><span className="label-text text-base-content/80">Alamat</span></label>
            <textarea
              placeholder="Alamat lengkap"
              className="textarea textarea-bordered w-full text-base-content"
              value={editedCustomerAddress}
              onChange={(e) => setEditedCustomerAddress(e.target.value)}
            />
          </div>
          <div className="modal-action mt-6">
            <button
              type="button"
              className="btn btn-sm"
              onClick={handleCloseEditCustomerModal}
            >
              Batal
            </button>
            <button
              type="button"
              className="btn btn-primary btn-sm"
              onClick={handleSaveEditedCustomer}
              disabled={!editedCustomerName || !editedCustomerPhone || isEditingCustomer}
            >
              {isEditingCustomer && <span className="loading loading-spinner loading-xs mr-1"></span>}
              Simpan Perubahan
            </button>
          </div>
        </div>
        <form method="dialog" className="modal-backdrop">
          <button type="button" onClick={handleCloseEditCustomerModal}>close</button>
        </form>
      </dialog>

      {/* Modal Progres Auto-Tagging */}
      {isAutoTagBatchProcessing && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-base-100 p-6 rounded-lg shadow-xl max-w-md w-full border border-base-300">
            <h3 className="font-bold text-lg text-secondary mb-4">Memproses Tag Pelanggan</h3>

            <div className="mb-4">
              <div className="flex justify-between mb-1">
                <span className="text-sm font-medium">Progres: {autoTagProgress}%</span>
                <span className="text-sm font-medium">Batch: {autoTagCurrentBatch}/{autoTagTotalBatches || '?'}</span>
              </div>
              <div className="w-full bg-base-200 rounded-full h-2.5">
                <div
                  className="bg-secondary h-2.5 rounded-full transition-all duration-300"
                  style={{ width: `${autoTagProgress}%` }}
                ></div>
              </div>
            </div>

            <div className="stats stats-vertical shadow w-full mb-4">
              <div className="stat">
                <div className="stat-title">Total Pelanggan</div>
                <div className="stat-value text-lg">{autoTagCumulativeResults.total}</div>
              </div>
              <div className="stat">
                <div className="stat-title">Pelanggan Diproses</div>
                <div className="stat-value text-lg">{autoTagCumulativeResults.processed}</div>
              </div>
              <div className="stat">
                <div className="stat-title">Pelanggan Diperbarui</div>
                <div className="stat-value text-lg">{autoTagCumulativeResults.updated}</div>
              </div>
            </div>

            <p className="text-sm text-base-content/70 italic">
              Proses ini berjalan secara bertahap untuk menghindari timeout. Mohon tunggu hingga selesai...
            </p>
          </div>
        </div>
      )}

      {/* Modal Hasil Auto-Tagging */}
      <dialog id="tag_result_modal" className={`modal ${isTagResultModalOpen ? 'modal-open' : ''}`}>
        <div className="modal-box max-w-md border border-base-300">
          <button
            type="button"
            onClick={handleCloseTagResultModal}
            className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2 z-10"
            aria-label="Tutup"
          >✕</button>
          <h3 className="font-bold text-lg text-secondary">Hasil Pengaturan Tag Otomatis</h3>
          <div className="py-4">
            <div className="stats stats-vertical shadow w-full">
              <div className="stat">
                <div className="stat-title">Total Pelanggan</div>
                <div className="stat-value">{tagResults.total}</div>
              </div>
              <div className="stat">
                <div className="stat-title">Pelanggan Diproses</div>
                <div className="stat-value text-info">{tagResults.processed}</div>
                <div className="stat-desc">
                  {tagResults.processed < tagResults.total ?
                    `${Math.round((tagResults.processed / tagResults.total) * 100)}% dari total pelanggan` :
                    'Semua pelanggan diproses'}
                </div>
              </div>
              <div className="stat">
                <div className="stat-title">Pelanggan Diperbarui</div>
                <div className="stat-value text-secondary">{tagResults.updated}</div>
                <div className="stat-desc">
                  {tagResults.updated > 0 ?
                    `${Math.round((tagResults.updated / tagResults.processed) * 100)}% dari pelanggan yang diproses` :
                    'Tidak ada perubahan tag'}
                </div>
              </div>
            </div>

            <div className="mt-4">
              <h4 className="font-semibold mb-2">Detail Tag:</h4>
              <div className="overflow-x-auto">
                <table className="table table-zebra w-full">
                  <thead>
                    <tr>
                      <th>Tag</th>
                      <th>Jumlah</th>
                      <th>Deskripsi</th>
                    </tr>
                  </thead>
                  <tbody>
                    {tagResults.details.map((item, index) => (
                      <tr key={index}>
                        <td>
                          <span className={`badge ${getStatusBadgeStyle(item.tag)}`}>{item.tag}</span>
                        </td>
                        <td>{item.count}</td>
                        <td className="text-sm">
                          {item.tag === 'A' && 'Pelanggan rutin (interval ≤30 hari)'}
                          {item.tag === 'B' && 'Kembali dalam 31-60 hari'}
                          {item.tag === 'C' && 'Kembali dalam 61-180 hari'}
                          {item.tag === 'D' && 'Kembali setelah >180 hari'}
                          {(item.tag === 'BARU' || item.tag === 'Baru') && 'Pelanggan baru atau belum memenuhi kriteria lain'}
                        </td>
                      </tr>
                    ))}
                    {tagResults.details.length === 0 && (
                      <tr>
                        <td colSpan={3} className="text-center py-4">Tidak ada data tag</td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="mt-4 p-4 bg-base-200 rounded-lg">
              <h4 className="font-semibold mb-2">Kriteria Tag:</h4>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center gap-2">
                  <span className="badge badge-primary">A</span>
                  <span>Pelanggan dengan minimal 2 transaksi dengan interval maksimal 30 hari</span>
                </li>
                <li className="flex items-center gap-2">
                  <span className="badge badge-secondary">B</span>
                  <span>Pelanggan yang kembali dalam 31-60 hari sejak kunjungan terakhir</span>
                </li>
                <li className="flex items-center gap-2">
                  <span className="badge badge-accent">C</span>
                  <span>Pelanggan yang kembali dalam 61-180 hari sejak kunjungan terakhir</span>
                </li>
                <li className="flex items-center gap-2">
                  <span className="badge badge-warning">D</span>
                  <span>Pelanggan yang kembali setelah lebih dari 180 hari</span>
                </li>
                <li className="flex items-center gap-2">
                  <span className="badge badge-info">Baru</span>
                  <span>Pelanggan baru atau belum memenuhi kriteria tag lainnya</span>
                </li>
              </ul>
            </div>

            <div className="mt-4 p-4 bg-yellow-50 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 rounded-lg text-sm">
              <h4 className="font-semibold mb-2 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                Catatan Penting
              </h4>
              <p>
                Proses auto-tag menggunakan metode batch untuk menghindari timeout.
                {tagResults.processed < tagResults.total ?
                  ` Hanya ${tagResults.processed} dari ${tagResults.total} pelanggan yang berhasil diproses. Silakan jalankan kembali untuk memproses pelanggan lainnya.` :
                  ' Semua pelanggan berhasil diproses.'}
              </p>
            </div>
          </div>
          <div className="modal-action">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={handleCloseTagResultModal}
              aria-label="Tutup dialog"
            >
              Tutup
            </button>
          </div>
        </div>
        <form method="dialog" className="modal-backdrop">
          <button
            type="button"
            onClick={handleCloseTagResultModal}
            aria-label="Tutup dialog"
          >
            close
          </button>
        </form>
      </dialog>

    </motion.div>
  );
}
