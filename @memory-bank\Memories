# Memories

This file contains memories from previous interactions between the AI assistant and the user.

## UI/UX Preferences
- User wants responsive pages with tables readable in light theme (default), colors matching logo.png, and utilizing existing Framer animations.
- User prefers modal dialogs instead of browser confirm() dialogs for confirmation actions.
- User prefers logos with borders/background circles, split text-like animations, and looping image animations with Framer.
- User prefers booking IDs in format B000020 using 'displayId' field.
- User prefers descriptive toast error messages for validation errors.
- User prefers dropdown menus with search functionality, especially for therapist selection.
- User prefers search filtering to not reload on every keystroke while maintaining previous filtering behavior.
- User prefers Michegar font and Split Text animation for the 'BADAN SEGAR URUSAN LANCAR' text on homepage.
- User prefers modern calendar interfaces like the one on the booking page for date filters, with consistency across components.
- User prefers Augment Memories to always direct to @memory-bank/ location and maintain memory bank functionality when adapting for Cursor AI.
- User prefers responses in Indonesian with emotes and does not want the 'Run Dev' command to be executed. 🥺🇮🇩

## Visual Design Elements
- User prefers the word 'breaktime' to follow the logo's color scheme with a brown gradient in the homepage hero section.
- User prefers specific background images: Makassar.jpeg (homepage), Emysaelan.jpeg, and Setiabudi.jpeg for respective outlet location pages.
- User prefers the 'kembali semangat' image positioned in the right corner with text at the far right edge, maintaining size when responsive.
- User prefers the total row in payment type tables to have distinctive coloring to make it more visually prominent.
- User prefers outlet names to be displayed without 'breaktime' prefix or 'palu' word in the UI.

## PWA Implementation
- User wants a Progressive Web App using existing images in public folders and the icons.json file, supporting landscape orientation.
- User prefers to skip Prisma generation step in the build process.

## Reports & Analytics
- User prioritizes Analytics & Reports features with API endpoints at src/app/api/reports/.
- User wants line charts showing daily revenue with weekly, monthly, previous period, and day-of-week comparison filters.
- User wants busy hours section to show services during specific hours when clicked, displaying all busy hours.
- User wants total revenue generated by each cashier, filtered by outlet location, without displaying usernames.
- User wants dashboard pages to display total amounts for each payment type with consistent period filtering across all data displays.
- User wants busy/quiet hours analysis to include gender data with automatic gender detection based on names.
- User prefers line chart visualization for busy hours by gender data with time range from 09:00-23:00 on the CRM page.
- User prefers to use Asia/Makassar timezone (WITA, GMT+8) for date/time operations in the application.
- User prefers tanggal 1 bulan berjalan as default start date for reports and modern calendar UI like in booking page.
- Investor users should not access busy hour analysis, frequent customers, and therapist performance data.

## Customer Management
- User wants customer loyalty points displayed on Booking and Transaction pages when a customer is selected.
- User wants new customers to automatically receive a 'Baru' tag without affecting existing tags.
- User wants a customer-facing page where customers can check loyalty points and visit history by entering their phone number.
- User wants customer page Excel exports to include address, phone numbers, transaction data from each outlet, and customer tags.
- User wants customer data to be filtered by the outlets where they have made transactions, with date filtering functionality.
- User wants a WhatsApp action button that opens wa.me API directly on the customer page.
- User wants a customer merge feature where the customer with the most transactions becomes the primary record.
- User wants a dedicated CRM page that can filter data by outlet (Makassar, Emysaelan, Setiabudi) to track customer types A/B/C/D per outlet.
- User wants customer categories (A/B/C/D) and tags (like 'Baru') to be properly displayed on the CRM page, with tags shown next to customer names.
- User prefers tag and category display in the CRM page to be consistent with implementation in the customer page, with proper tag formatting and display in the UI.
- When migrating customer data from Excel, preserve existing tags from the Excel file rather than overwriting them.

## Booking & Transactions
- User wants the booking page to allow selecting the same service multiple times and to support selecting two customers per booking.
- User wants WhatsApp confirmation buttons on booking list with messages including emojis, loyalty points, and booking details.
- User wants functionality to mark busy therapists as available when customers add services during their session.
- User prefers transaction history to display all selected services with total quantities, especially with multiple instances of the same service.
- User prefers booking tables, summaries, and WhatsApp confirmations to display names of all selected customers.
- User wants an Excel download button on the transaction history page at src/app/dashboard/riwayat/.
- User prefers transaction receipts to display discount and additional charge information only when their values are not 0.
- User wants an online booking feature for customers that allows outlet selection, randomizes therapist assignment, and includes WhatsApp confirmation to 081248961009 using the API with pending status.

## Services & Therapists
- User wants Services page to allow setting service prices to 0 and special commission rates for specific therapists.
- User wants to hide history and detail actions from staff users on the therapist page.
- User wants therapist history modal to display total sales and commission information in card format with date filters.
- User wants therapist commission check page to display data from all outlets where the therapist has served customers.
- User wants captain management features where captains (selected from STAFF users) can view total income of assigned therapists.
- User prefers to display the captain's name in the therapist table at src/app/dashboard/terapis/.
- User prefers to display the original transaction amount even when there's a discount to properly track therapist income.
- User wants therapist commission data to be sourced from src/app/api/transactions/ endpoint for more accurate data display.

## User Management
- Only admin users should see sidebar links to form.mybreaktime.co.id and finance.mybreaktime.co.id.
- User prefers the Users page to sort users by role with automatic outlet selection for new investor users based on permissions.
- User prefers to only show staff users in the captain selection dropdown, not all user types.
- User prefers the taskbar username display to be clickable with a dropdown option to change password.
- User prefers to modify database schemas without resetting existing data, particularly for the captainUserId field.

## Export & Development
- User prefers Excel exports to have appropriate column widths and filtering capabilities.
- User prefers PDF outputs to have a more professional appearance.
- There's a difference in how the application behaves in development mode versus when deployed on Vercel that needs to be fixed.

## General Preferences
- User prefers rules and documentation to be provided in English language.

## Attendance System
- ID Card system untuk terapis dan staff telah dikembangkan dengan QR code untuk absensi.
- Komponen IDCard menampilkan logo Breaktime dengan background lingkaran putih, nama pengguna, jabatan, avatar (inisial/foto), QR code, dan nomor ID.
- Generator ID Card menyediakan opsi untuk memilih terapis atau staff, upload foto profil (opsional), dan kustomisasi teks jabatan.
- Sistem dilengkapi fitur untuk mencetak ID Card dan menyimpan ID Card sebagai gambar HD menggunakan html2canvas/htmlToImage.
- Desain kartu menggunakan dekorasi bubble, gradient header, dan border putih untuk avatar sesuai dengan referensi pengguna. 