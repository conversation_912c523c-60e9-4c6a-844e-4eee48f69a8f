import "./globals.css"
import type { Metadata } from "next"
import { <PERSON><PERSON><PERSON> } from "next/font/google"
import { ThemeProvider } from "@/contexts/ThemeContext"
import { OutletProvider } from "@/contexts/OutletContext"
import { AuthProvider } from "@/contexts/AuthContext"
import { Toaster } from 'sonner'
import Script from 'next/script'

const poppins = Poppins({
  weight: ['400', '600', '700'],
  subsets: ["latin"],
  display: 'swap',
});

export const metadata: Metadata = {
  title: "Breaktime - Pijat Relaksasi",
  description: "Temukan relaksasi sejati bersama Breaktime.",
  manifest: "/manifest.json",
  themeColor: "#10b981",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "Breaktime",
  },
  viewport: {
    width: "device-width",
    initialScale: 1,
    maximumScale: 5,
    userScalable: true,
  },
  icons: {
    icon: [
      { url: "/android/android-launchericon-192-192.png", sizes: "192x192", type: "image/png" },
      { url: "/android/android-launchericon-512-512.png", sizes: "512x512", type: "image/png" }
    ],
    apple: [
      { url: "/ios/180.png", sizes: "180x180", type: "image/png" },
      { url: "/ios/152.png", sizes: "152x152", type: "image/png" }
    ],
    other: [
      { url: "/windows11/Square150x150Logo.scale-100.png", sizes: "150x150", type: "image/png" }
    ]
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="id" data-theme="breaktime">
      <head>
        <meta name="application-name" content="Breaktime" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Breaktime" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-TileColor" content="#10b981" />
        <meta name="msapplication-tap-highlight" content="no" />
        <meta name="theme-color" content="#10b981" />
        <meta name="orientation" content="any" />

        <link rel="apple-touch-icon" href="/ios/180.png" />
        <link rel="apple-touch-icon" sizes="152x152" href="/ios/152.png" />
        <link rel="apple-touch-icon" sizes="180x180" href="/ios/180.png" />
        <link rel="apple-touch-icon" sizes="167x167" href="/ios/167.png" />

        <link rel="icon" type="image/png" sizes="32x32" href="/ios/32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/ios/16.png" />
        <link rel="manifest" href="/manifest.json" />
        <link rel="shortcut icon" href="/favicon.ico" />
      </head>
      <body className={`${poppins.className} antialiased bg-gray-100`}>
        <AuthProvider>
          <ThemeProvider>
            <OutletProvider>
              {children}
              <Toaster position="top-right" richColors />
            </OutletProvider>
          </ThemeProvider>
        </AuthProvider>

        {/* Service Worker Registration */}
        <Script src="/sw-register.js" strategy="lazyOnload" />
      </body>
    </html>
  )
}
