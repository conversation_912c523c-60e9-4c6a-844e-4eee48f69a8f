# Reports & Analytics

*Dokumen ini berisi preferensi dan kebutuhan terkait fitur laporan dan analitik untuk aplikasi Breaktime.*

## Prioritas dan Struktur

- Fitur Analytics & Reports diprioritaskan dengan endpoint API di src/app/api/reports/
- Grafik garis yang menunjukkan pendapatan harian dengan filter:
  - Mingguan
  - Bulanan
  - Periode sebelumnya
  - Perbandingan hari dalam seminggu

## Analisis Jam Sibuk

- Bagian jam sibuk menampilkan layanan selama jam tertentu saat diklik
- Menampilkan semua jam sibuk
- Analisis jam sibuk/tenang menyertakan data gender dengan deteksi gender otomatis berdasarkan nama
- Visualisasi grafik garis untuk data jam sibuk berdasarkan gender dengan rentang waktu 09:00-23:00 di halaman CRM

## Pendapatan dan Kinerja

- Total pendapatan yang dihasilkan oleh setiap kasir, difilter berdasarkan lokasi outlet, tanpa menampilkan username
- Pengguna investor tidak boleh mengakses analisis jam sibuk, pelanggan tetap, dan data kinerja terapis
- Halaman dashboard menampilkan jumlah total untuk setiap jenis pembayaran dengan filter periode yang konsisten di semua tampilan data

## Akses dan Pembatasan

- Pengguna investor tidak boleh mengakses:
  - Analisis jam sibuk
  - Data pelanggan tetap
  - Data kinerja terapis
