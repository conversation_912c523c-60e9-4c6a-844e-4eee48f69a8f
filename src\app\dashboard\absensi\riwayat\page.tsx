'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-hot-toast';
import { 
  ChevronLeftIcon, 
  ChevronRightIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
  DocumentArrowDownIcon,
  BuildingOfficeIcon,
  UserIcon,
  UserGroupIcon,
  ClockIcon,
  CalendarIcon,
  DocumentTextIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  TableCellsIcon,
  ViewColumnsIcon,
  TrashIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import AttendanceFilter from '@/components/attendance/AttendanceFilter';
import DateFormatter from '@/components/attendance/DateFormatter';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';

interface Attendance {
  id: string;
  userId: string;
  name: string;
  role: string;
  outletId: string;
  outletName: string;
  attendanceType: string;
  timestamp: string;
  type: string;
  notes: string;
}

interface Filter {
  startDate?: string;
  endDate?: string;
  outletId?: string;
  type?: string;
}

interface Outlet {
  id: string;
  name: string;
}

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      when: "beforeChildren",
      staggerChildren: 0.08, // Slightly faster stagger
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: { type: 'spring', stiffness: 120, damping: 18 } // Adjusted spring
  }
};

const AbsensiRiwayatPage = () => {
  const [attendances, setAttendances] = useState<Attendance[]>([]);
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [exporting, setExporting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  
  // State untuk konfirmasi hapus
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [attendanceToDelete, setAttendanceToDelete] = useState<string | null>(null);
  const deleteModalRef = useRef<HTMLDialogElement>(null);
  
  // Auth context untuk cek role admin
  const { user } = useAuth();
  const isAdmin = user?.role === 'ADMIN';
  
  // State untuk pagination
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [limit, setLimit] = useState(50);
  
  // State untuk filter
  const [filters, setFilters] = useState<Filter>({});
  
  // State untuk tampilan data (tabel atau card)
  const [viewMode, setViewMode] = useState<'table' | 'card'>('table');
  
  // State untuk search query
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredAttendances, setFilteredAttendances] = useState<Attendance[]>([]);

  // Fetch data absensi
  const fetchAttendances = async () => {
    setLoading(true);
    setError('');
    
    try {
      // Buat URL dengan parameter filter
      let url = `/api/attendance?page=${page}&limit=${limit}`;
      
      if (filters.startDate) {
        url += `&startDate=${filters.startDate}`;
      }
      
      if (filters.endDate) {
        url += `&endDate=${filters.endDate}`;
      }
      
      if (filters.outletId) {
        url += `&outletId=${filters.outletId}`;
      }
      
      if (filters.type) {
        url += `&type=${filters.type}`;
      }
      
      const response = await fetch(url);
      const result = await response.json();
      
      if (response.ok) {
        setAttendances(result.data);
        setFilteredAttendances(result.data);
        setOutlets(result.filters.outlets);
        setTotalPages(result.pagination.totalPages);
        setTotalItems(result.pagination.totalItems);
      } else {
        setError(result.error || 'Gagal mengambil data absensi');
        toast.error(result.error || 'Gagal mengambil data absensi');
      }
    } catch (err) {
      console.error('Error fetching attendance data:', err);
      setError('Terjadi kesalahan saat mengambil data');
      toast.error('Terjadi kesalahan saat mengambil data');
    } finally {
      setLoading(false);
    }
  };
  
  // Load data saat halaman pertama kali diload dan saat filter berubah
  useEffect(() => {
    fetchAttendances();
  }, [page, limit, filters]);

  // Filter data berdasarkan search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredAttendances(attendances);
      return;
    }
    
    const lowerQuery = searchQuery.toLowerCase();
    const filtered = attendances.filter(
      (attendance) => 
        attendance.name.toLowerCase().includes(lowerQuery) ||
        attendance.outletName.toLowerCase().includes(lowerQuery) ||
        attendance.role.toLowerCase().includes(lowerQuery) ||
        attendance.notes.toLowerCase().includes(lowerQuery)
    );
    
    setFilteredAttendances(filtered);
  }, [searchQuery, attendances]);

  // Effect untuk modal delete
  useEffect(() => {
    if (showDeleteModal && deleteModalRef.current) {
      deleteModalRef.current.showModal();
    } else if (!showDeleteModal && deleteModalRef.current) {
      deleteModalRef.current.close();
    }
  }, [showDeleteModal]);

  // Handle hapus riwayat absensi
  const handleDeleteAttendance = async (id: string) => {
    setAttendanceToDelete(id);
    setShowDeleteModal(true);
  };
  
  // Konfirmasi hapus
  const confirmDelete = async () => {
    if (!attendanceToDelete) return;
    
    setIsDeleting(true);
    
    try {
      const response = await fetch(`/api/attendance/${attendanceToDelete}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Gagal menghapus riwayat absensi');
      }
      
      // Hapus dari state
      setAttendances(prevAttendances => 
        prevAttendances.filter(a => a.id !== attendanceToDelete)
      );
      
      setFilteredAttendances(prevAttendances => 
        prevAttendances.filter(a => a.id !== attendanceToDelete)
      );
      
      toast.success('Riwayat absensi berhasil dihapus');
    } catch (error) {
      console.error('Error deleting attendance:', error);
      toast.error(error instanceof Error ? error.message : 'Terjadi kesalahan');
    } finally {
      setIsDeleting(false);
      setShowDeleteModal(false);
      setAttendanceToDelete(null);
    }
  };

  // Handle filter change
  const handleFilter = (newFilters: Filter) => {
    setPage(1); // Reset ke halaman pertama saat filter berubah
    setFilters(newFilters);
  };
  
  // Handle pagination
  const goToPage = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setPage(newPage);
    }
  };
  
  // Function untuk mendapatkan warna badge berdasarkan tipe dan status
  const getBadgeColor = (type: string, attendanceType: string) => {
    if (type === 'therapist') {
      return attendanceType === 'IN' ? 'bg-emerald-100 text-emerald-800' : 'bg-blue-100 text-blue-800';
    } else {
      return attendanceType === 'IN' ? 'bg-purple-100 text-purple-800' : 'bg-amber-100 text-amber-800';
    }
  };
  
  // Handle export to Excel
  const handleExportExcel = async () => {
    setExporting(true);
    
    try {
      // Buat URL dengan parameter filter untuk export
      let url = `/api/attendance/export-excel?`;
      
      if (filters.startDate) {
        url += `&startDate=${filters.startDate}`;
      }
      
      if (filters.endDate) {
        url += `&endDate=${filters.endDate}`;
      }
      
      if (filters.outletId) {
        url += `&outletId=${filters.outletId}`;
      }
      
      if (filters.type) {
        url += `&type=${filters.type}`;
      }
      
      // Tampilkan toast loading
      const loadingToast = toast.loading('Sedang memproses ekspor data...');
      
      // Fetch sebagai blob
      const response = await fetch(url);
      
      // Dismiss loading toast
      toast.dismiss(loadingToast);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Gagal mengekspor data');
      }
      
      // Convert response ke blob
      const blob = await response.blob();
      
      // Create download link
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      
      // Get current date for filename
      const date = new Date();
      const formattedDate = `${date.getFullYear()}${String(date.getMonth() + 1).padStart(2, '0')}${String(date.getDate()).padStart(2, '0')}`;
      
      link.setAttribute('download', `attendance_report_${formattedDate}.xlsx`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      
      // Cleanup
      window.URL.revokeObjectURL(downloadUrl);
      
      toast.success('Data berhasil diekspor ke Excel');
    } catch (err) {
      console.error('Error exporting data:', err);
      toast.error(`Gagal mengekspor data: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setExporting(false);
    }
  };

  // Render pagination controls
  const renderPagination = () => {
    return (
      <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-b-lg">
        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p className="text-sm text-gray-700">
              Menampilkan <span className="font-medium">{(page - 1) * limit + 1}</span> - <span className="font-medium">{
                Math.min(page * limit, totalItems)
              }</span> dari <span className="font-medium">{totalItems}</span> hasil
            </p>
          </div>
          <div>
            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                onClick={() => goToPage(page - 1)}
                disabled={page === 1}
                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                aria-label="Previous page"
              >
                <span className="sr-only">Previous</span>
                <ChevronLeftIcon className="h-5 w-5" aria-hidden="true" />
              </button>
              
              {/* Page numbers */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                // Logic untuk menampilkan 5 halaman dengan halaman aktif di tengah jika memungkinkan
                let pageNum = 1;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (page <= 3) {
                  pageNum = i + 1;
                } else if (page >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = page - 2 + i;
                }
                
                return (
                  <button
                    key={i}
                    onClick={() => goToPage(pageNum)}
                    className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                      pageNum === page
                        ? 'z-10 bg-[#4EA799]/10 border-[#4EA799] text-[#4EA799]'
                        : 'bg-white border-gray-300 text-gray-500 hover:bg-[#F7BF45]/15 hover:border-[#F7BF45] hover:text-[#F7BF45]'
                    }`}
                    aria-label={`Page ${pageNum}`}
                    aria-current={pageNum === page ? "page" : undefined}
                  >
                    {pageNum}
                  </button>
                );
              })}
              
              <button
                onClick={() => goToPage(page + 1)}
                disabled={page === totalPages}
                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                aria-label="Next page"
              >
                <span className="sr-only">Next</span>
                <ChevronRightIcon className="h-5 w-5" aria-hidden="true" />
              </button>
            </nav>
          </div>
        </div>
        
        <div className="flex items-center space-x-2 sm:hidden">
          <button
            onClick={() => goToPage(page - 1)}
            disabled={page === 1}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label="Previous page"
          >
            <ChevronLeftIcon className="h-5 w-5 mr-1" />
            Prev
          </button>
          
          <span className="text-sm text-gray-500">
            {page} / {totalPages}
          </span>
          
          <button
            onClick={() => goToPage(page + 1)}
            disabled={page === totalPages}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label="Next page"
          >
            Next
            <ChevronRightIcon className="h-5 w-5 ml-1" />
          </button>
        </div>
      </div>
    );
  };

  return (
    <motion.div
      className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 min-h-screen bg-gradient-to-br from-slate-50 to-sky-100" // Gradient background
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header dengan Tombol Kembali */}
      <motion.div
        variants={itemVariants}
        className="mb-8 p-6 rounded-xl shadow-lg bg-white/80 backdrop-blur-md border border-gray-200" // Refined header card
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <CalendarIcon className="w-10 h-10 text-[#4EA799] mr-4" />
            <div>
              <h1 className="text-3xl font-bold text-gray-800">Riwayat Absensi</h1>
              <p className="text-gray-600 mt-1">Pantau dan kelola catatan kehadiran terapis dan staff.</p>
            </div>
          </div>
          <Link href="/dashboard/absensi" className="inline-flex items-center text-sm font-medium text-[#4EA799] hover:text-[#F7BF45] transition-colors py-2 px-4 rounded-lg hover:bg-[#4EA799]/10">
            <ChevronLeftIcon className="w-5 h-5 mr-1" />
            <span>Kembali ke Absensi</span>
          </Link>
        </div>
      </motion.div>
      
      {/* Filter */}
      <motion.div variants={itemVariants} className="mb-6">
        <AttendanceFilter
          onFilter={handleFilter}
          outlets={outlets}
          loading={loading}
          initialFilters={filters}
        />
      </motion.div>
      
      {/* Toolbar */}
      <motion.div
        variants={itemVariants}
        className="bg-white/70 backdrop-blur-md border border-gray-200 rounded-xl shadow-md mb-6 p-4 flex flex-col sm:flex-row gap-4 justify-between items-center" // Refined toolbar card
      >
        {/* Search Bar */}
        <div className="relative flex-grow w-full sm:max-w-sm md:max-w-md">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-lg text-gray-700 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#4EA799] focus:border-transparent sm:text-sm transition-shadow hover:shadow-sm"
            placeholder="Cari nama, outlet, peran..."
            aria-label="Search"
          />
        </div>
        
        <div className="flex items-center gap-3 w-full sm:w-auto">
          {/* View Switcher */}
          <div className="flex rounded-lg shadow-sm border border-gray-300 overflow-hidden">
            <button
              type="button"
              onClick={() => setViewMode('table')}
              className={`relative inline-flex items-center px-3 py-2 text-sm font-medium transition-colors ${
                viewMode === 'table'
                  ? 'bg-[#4EA799] text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100'
              }`}
              aria-label="View as Table"
            >
              <TableCellsIcon className="h-5 w-5" />
            </button>
            <button
              type="button"
              onClick={() => setViewMode('card')}
              className={`relative inline-flex items-center px-3 py-2 text-sm font-medium transition-colors border-l border-gray-300 ${
                viewMode === 'card'
                  ? 'bg-[#4EA799] text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-100'
              }`}
              aria-label="View as Cards"
            >
              <ViewColumnsIcon className="h-5 w-5" />
            </button>
          </div>
          
          {/* Export Button */}
          <motion.button
            whileHover={{ scale: 1.03, y: -1 }}
            whileTap={{ scale: 0.97 }}
            className={`inline-flex items-center px-4 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-[#4EA799] hover:bg-[#3d8a7e] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#4EA799] transition-all ${
              exporting || loading || filteredAttendances.length === 0 ? 'opacity-60 cursor-not-allowed' : ''
            }`}
            onClick={handleExportExcel}
            disabled={exporting || loading || filteredAttendances.length === 0}
          >
            {exporting ? (
              <ArrowPathIcon className="animate-spin h-5 w-5 mr-2" />
            ) : (
              <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
            )}
            {exporting ? 'Eksporting...' : 'Export Excel'}
          </motion.button>
        </div>
      </motion.div>
      
      {/* Data Display */}
      <motion.div
        variants={itemVariants}
        className="bg-white/70 backdrop-blur-md border border-gray-200 rounded-xl shadow-lg overflow-hidden" // Refined data display card
      >
        {loading ? (
          <div className="flex flex-col items-center justify-center py-20 text-center">
            <motion.div animate={{ rotate: 360 }} transition={{ duration: 1, repeat: Infinity, ease: "linear" }}>
              <ArrowPathIcon className="h-16 w-16 text-[#F7BF45] mb-6" />
            </motion.div>
            <p className="text-xl font-semibold text-gray-700">Memuat data absensi...</p>
            <p className="text-gray-500">Mohon tunggu sebentar.</p>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center py-20 text-center">
            <XCircleIcon className="h-16 w-16 text-red-500 mb-6" />
            <p className="text-xl font-semibold text-red-600">{error}</p>
            <p className="text-gray-500 mb-6">Terjadi kesalahan saat mengambil data.</p>
            <button
              className="mt-4 inline-flex items-center px-6 py-2.5 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-[#4EA799] hover:bg-[#3d8a7e] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#4EA799] transition-colors"
              onClick={() => fetchAttendances()}
            >
              <ArrowPathIcon className="h-5 w-5 mr-2" />
              Coba Lagi
            </button>
          </div>
        ) : filteredAttendances.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-20 text-center">
            <DocumentTextIcon className="h-16 w-16 text-gray-400 mb-6" />
            <p className="text-xl font-semibold text-gray-700">Tidak Ada Data Ditemukan</p>
            <p className="text-gray-500">
              {searchQuery ? "Tidak ada hasil yang cocok dengan pencarian Anda." : "Belum ada riwayat absensi yang tercatat."}
            </p>
            {searchQuery && (
              <button
                className="mt-4 text-sm font-medium text-[#4EA799] hover:text-[#3d8a7e] transition-colors"
                onClick={() => setSearchQuery('')}
              >
                Hapus filter pencarian
              </button>
            )}
          </div>
        ) : viewMode === 'table' ? (
          // Table View
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200/70">
              <thead className="bg-gray-50/80">
                <tr>
                  <th scope="col" className="px-6 py-3.5 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    Nama
                  </th>
                  <th scope="col" className="px-6 py-3.5 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    Tipe / Role
                  </th>
                  <th scope="col" className="px-6 py-3.5 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    Outlet
                  </th>
                  <th scope="col" className="px-6 py-3.5 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3.5 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    Waktu
                  </th>
                  <th scope="col" className="px-6 py-3.5 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    Catatan
                  </th>
                  {isAdmin && (
                    <th scope="col" className="px-6 py-3.5 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">
                      Aksi
                    </th>
                  )}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200/70">
                {filteredAttendances.map((attendance) => (
                  <motion.tr
                    key={attendance.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.3 }}
                    className="hover:bg-sky-50/50 transition-colors"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{attendance.name}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span
                        className={`px-2.5 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          attendance.type === 'therapist'
                            ? 'bg-sky-100 text-sky-800'
                            : 'bg-violet-100 text-violet-800'
                        }`}
                      >
                        {attendance.type === 'therapist' ? 'Terapis' : 'Staff'}
                      </span>
                      <div className="text-xs text-gray-400 mt-0.5">{attendance.role}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                      {attendance.outletName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2.5 py-1 inline-flex text-xs leading-5 font-semibold rounded-full items-center ${
                          attendance.attendanceType === 'IN'
                            ? 'bg-emerald-100 text-emerald-800'
                            : 'bg-amber-100 text-amber-800'
                        }`}
                      >
                        {attendance.attendanceType === 'IN' ? (
                          <>
                            <CheckCircleIcon className="w-4 h-4 mr-1.5" />
                            Masuk
                          </>
                        ) : (
                          <>
                            <XCircleIcon className="w-4 h-4 mr-1.5" />
                            Keluar
                          </>
                        )}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                      <DateFormatter date={attendance.timestamp} />
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500 max-w-xs">
                      <span className="block truncate" title={attendance.notes || "-"}>{attendance.notes || "-"}</span>
                    </td>
                    {isAdmin && (
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-center">
                        <button
                          onClick={() => handleDeleteAttendance(attendance.id)}
                          className="text-red-500 hover:text-red-700 p-1.5 rounded-md hover:bg-red-100/50 transition-colors focus:outline-none"
                          title="Hapus riwayat"
                        >
                          <TrashIcon className="w-5 h-5" />
                        </button>
                      </td>
                    )}
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          // Card View
          <div className="p-4 sm:p-6 grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-5">
            {filteredAttendances.map((attendance) => (
              <motion.div
                key={attendance.id}
                initial={{ opacity: 0, y: 15 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: filteredAttendances.indexOf(attendance) * 0.03 }}
                className="bg-white rounded-xl border border-gray-200/80 shadow-lg hover:shadow-xl transition-all p-5 flex flex-col justify-between"
              >
                <div>
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex items-center">
                      <div className={`h-11 w-11 rounded-full flex items-center justify-center mr-3.5 shrink-0 ${
                        attendance.type === 'therapist'
                          ? 'bg-[#4EA799]/10'
                          : 'bg-[#F7BF45]/15'
                      }`}>
                        {attendance.type === 'therapist' ?
                          <UserGroupIcon className="h-6 w-6 text-[#4EA799]" /> :
                          <UserIcon className="h-6 w-6 text-[#F7BF45]" />
                        }
                      </div>
                      <div>
                        <h3 className="text-md font-semibold text-gray-800">{attendance.name}</h3>
                        <p className="text-xs text-gray-500">{attendance.role}</p>
                      </div>
                    </div>
                    <span
                      className={`px-2.5 py-1 inline-flex text-xs leading-4 font-semibold rounded-full items-center shrink-0 ${
                        attendance.attendanceType === 'IN'
                          ? 'bg-emerald-100 text-emerald-800'
                          : 'bg-amber-100 text-amber-800'
                      }`}
                    >
                      {attendance.attendanceType === 'IN' ? (
                        <>
                          <CheckCircleIcon className="w-3.5 h-3.5 mr-1" />
                          Masuk
                        </>
                      ) : (
                        <>
                          <XCircleIcon className="w-3.5 h-3.5 mr-1" />
                          Keluar
                        </>
                      )}
                    </span>
                  </div>

                  <div className="mt-4 space-y-2.5 text-sm">
                    <div className="flex items-center text-gray-700">
                      <BuildingOfficeIcon className="w-5 h-5 mr-3 text-gray-400 shrink-0" />
                      {attendance.outletName}
                    </div>
                    <div className="flex items-center text-gray-700">
                      <CalendarIcon className="w-5 h-5 mr-3 text-gray-400 shrink-0" />
                      <DateFormatter date={attendance.timestamp} format="EEEE, dd MMM yyyy" />
                    </div>
                    <div className="flex items-center text-gray-700">
                      <ClockIcon className="w-5 h-5 mr-3 text-gray-400 shrink-0" />
                      <DateFormatter date={attendance.timestamp} format="HH:mm:ss" />
                    </div>
                    {attendance.notes && (
                      <div className="flex items-start text-gray-700 pt-2 mt-2.5 border-t border-gray-100">
                        <DocumentTextIcon className="w-5 h-5 mr-3 text-gray-400 mt-0.5 shrink-0" />
                        <p className="text-gray-600 break-words text-xs leading-relaxed">{attendance.notes}</p>
                      </div>
                    )}
                  </div>
                </div>

                {isAdmin && (
                  <div className="pt-3 mt-4 border-t border-gray-100 flex justify-end">
                    <button
                      onClick={() => handleDeleteAttendance(attendance.id)}
                      className="inline-flex items-center text-red-600 hover:text-red-800 text-xs font-medium p-2 rounded-md hover:bg-red-100/60 transition-colors"
                      title="Hapus data absensi ini"
                    >
                      <TrashIcon className="w-4 h-4 mr-1.5" />
                      Hapus
                    </button>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        )}
        
        {/* Pagination */}
        {!loading && !error && filteredAttendances.length > 0 && (
          <div className="bg-white/50 backdrop-blur-sm px-4 py-4 flex items-center justify-between border-t border-gray-200/80 sm:px-6 rounded-b-xl">
             {/* ... Isi pagination (copy dari renderPagination(), pastikan class sesuai) ... */}
             <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Menampilkan <span className="font-semibold">{(page - 1) * limit + 1}</span> - <span className="font-semibold">{
                      Math.min(page * limit, totalItems)
                    }</span> dari <span className="font-semibold">{totalItems}</span> hasil
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-lg shadow-sm -space-x-px" aria-label="Pagination">
                    <button
                      onClick={() => goToPage(page - 1)}
                      disabled={page === 1}
                      className="relative inline-flex items-center px-2.5 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      aria-label="Previous page"
                    >
                      <span className="sr-only">Previous</span>
                      <ChevronLeftIcon className="h-5 w-5" aria-hidden="true" />
                    </button>

                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let pageNum = 1;
                      if (totalPages <= 5) {
                        pageNum = i + 1;
                      } else if (page <= 3) {
                        pageNum = i + 1;
                      } else if (page >= totalPages - 2) {
                        pageNum = totalPages - 4 + i;
                      } else {
                        pageNum = page - 2 + i;
                      }

                      return (
                        <button
                          key={i}
                          onClick={() => goToPage(pageNum)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium transition-colors ${
                            pageNum === page
                              ? 'z-10 bg-[#4EA799]/15 border-[#4EA799] text-[#4EA799] font-semibold'
                              : 'bg-white border-gray-300 text-gray-500 hover:bg-[#F7BF45]/15 hover:border-[#F7BF45] hover:text-[#F7BF45]'
                          }`}
                          aria-label={`Page ${pageNum}`}
                          aria-current={pageNum === page ? "page" : undefined}
                        >
                          {pageNum}
                        </button>
                      );
                    })}

                    <button
                      onClick={() => goToPage(page + 1)}
                      disabled={page === totalPages}
                      className="relative inline-flex items-center px-2.5 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      aria-label="Next page"
                    >
                      <span className="sr-only">Next</span>
                      <ChevronRightIcon className="h-5 w-5" aria-hidden="true" />
                    </button>
                  </nav>
                </div>
              </div>
              <div className="flex items-center space-x-2 sm:hidden">
                  <button
                      onClick={() => goToPage(page - 1)}
                      disabled={page === 1}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      aria-label="Previous page"
                    >
                      <ChevronLeftIcon className="h-5 w-5 mr-1" />
                      Prev
                  </button>
                  <span className="text-sm text-gray-600">
                      {page} / {totalPages}
                  </span>
                  <button
                      onClick={() => goToPage(page + 1)}
                      disabled={page === totalPages}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      aria-label="Next page"
                    >
                      Next
                      <ChevronRightIcon className="h-5 w-5 ml-1" />
                  </button>
              </div>
          </div>
        )}
      </motion.div>
      
      {/* Modal Konfirmasi Hapus */}
      <dialog ref={deleteModalRef} className="modal modal-bottom sm:modal-middle" onClose={() => setShowDeleteModal(false)}>
        <div className="modal-box bg-white rounded-xl shadow-xl p-6">
          <div className="flex items-start">
            <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
              <ExclamationTriangleIcon className="h-6 w-6 text-red-600" aria-hidden="true" />
            </div>
            <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
              <h3 className="text-lg leading-6 font-semibold text-gray-900">
                Konfirmasi Hapus Data
              </h3>
              <div className="mt-2">
                <p className="text-sm text-gray-600">
                  Apakah Anda yakin ingin menghapus data riwayat absensi ini? Tindakan ini tidak dapat dibatalkan dan data akan hilang permanen.
                </p>
              </div>
            </div>
          </div>
          <div className="mt-5 sm:mt-6 sm:flex sm:flex-row-reverse gap-3">
            <button
              type="button"
              className={`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:w-auto sm:text-sm transition-colors ${isDeleting ? 'opacity-70 cursor-wait' : ''}`}
              onClick={confirmDelete}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <ArrowPathIcon className="w-5 h-5 mr-2 animate-spin" />
                  Menghapus...
                </>
              ) : (
                'Ya, Hapus Data'
              )}
            </button>
            <button
              type="button"
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm transition-colors"
              onClick={() => {
                setShowDeleteModal(false);
                setAttendanceToDelete(null);
              }}
              disabled={isDeleting}
            >
              Batal
            </button>
          </div>
        </div>
        <form method="dialog" className="modal-backdrop">
          <button type="button" onClick={() => setShowDeleteModal(false)}>close</button>
        </form>
      </dialog>
    </motion.div>
  );
};

export default AbsensiRiwayatPage; 