'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useOutletContext } from '@/contexts/OutletContext';
import { toast } from 'react-hot-toast';
import {
  Fi<PERSON><PERSON><PERSON>, <PERSON><PERSON>ilter, <PERSON>BarChart2, <PERSON><PERSON><PERSON>hart, FiAlertTriangle,
  FiRefreshCw, FiChevronLeft, FiDownload, FiCalendar, FiMapPin,
  FiEye, FiUser, FiPhone, FiMail, FiTag, FiInfo
} from 'react-icons/fi';
import Link from 'next/link';
import { format, subDays, differenceInDays } from 'date-fns';
import { id as idLocale } from 'date-fns/locale';
import * as XLSX from 'xlsx';
import { jsPDF } from "jspdf";
import autoTable from 'jspdf-autotable';
import { ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar, <PERSON>Chart, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';

// Varian animasi
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
};

// Interfaces
interface Customer {
  id: string;
  name: string;
  gender: 'MALE' | 'FEMALE' | 'OTHER' | null;
  totalVisits: number;
  totalSpent: number;
  visitsInRange: number;
  spentInRange: number;
  lastVisit: string;
  category: 'A' | 'B' | 'C' | 'D';
  outletId: string;
  outletName: string;
  rScore?: number;
  fScore?: number;
  mScore?: number;
  rfmSegment?: string;
  tags?: string[];
}

interface PeakHourData {
  hour: number;
  count: number;
  maleCount: number;
  femaleCount: number;
  otherCount: number;
}

interface Outlet {
  id: string;
  name: string;
}

interface CustomerCategoryData {
  outletId: string;
  outletName: string;
  categoryA: number;
  categoryB: number;
  categoryC: number;
  categoryD: number;
  total: number;
}

interface CRMData {
  customers: Customer[];
  peakHours: PeakHourData[];
  customerCategories: CustomerCategoryData[];
}

export default function CRMPage() {
  const { selectedOutletId } = useOutletContext();
  const [outletName, setOutletName] = useState<string>('');
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [filteredOutletId, setFilteredOutletId] = useState<string | null>(null);
  const [startDate, setStartDate] = useState<Date>(subDays(new Date(), 30)); // Default 30 hari terakhir
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [crmData, setCrmData] = useState<CRMData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeChartTab, setActiveChartTab] = useState<'bar' | 'line'>('bar');
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<'A' | 'B' | 'C' | 'D' | null>(null);
  const [selectedOutletForModal, setSelectedOutletForModal] = useState<string | null>(null);
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([]);
  const [processedCrmData, setProcessedCrmData] = useState<CRMData | null>(null); // State baru untuk data RFM
  const [showGenderAnalysisModal, setShowGenderAnalysisModal] = useState(false);

  // Efek untuk memuat data outlet
  useEffect(() => {
    const fetchOutlets = async () => {
      try {
        const response = await fetch('/api/outlets');
        if (!response.ok) {
          throw new Error('Gagal memuat data outlet');
        }
        const data = await response.json();
        setOutlets(data.outlets || []);
      } catch (error) {
        console.error('Error fetching outlets:', error);
        setError(error instanceof Error ? error.message : 'Terjadi kesalahan saat memuat data outlet');
      }
    };

    fetchOutlets();
  }, []);

  // Efek untuk memuat data CRM
  useEffect(() => {
    if (selectedOutletId !== null) {
      setIsLoading(true);
      setError(null);

      const fetchCRMData = async () => {
        try {
          // Format tanggal untuk API
          const startDateStr = format(startDate, 'yyyy-MM-dd');
          const endDateStr = format(endDate, 'yyyy-MM-dd');

          // Gunakan filteredOutletId jika ada, jika tidak gunakan selectedOutletId
          const outletIdParam = filteredOutletId || 'all';

          // Fetch data CRM dari API
          const response = await fetch(
            `/api/reports/crm?startDate=${startDateStr}&endDate=${endDateStr}&outletId=${outletIdParam}`
          );

          if (!response.ok) {
            throw new Error(`Gagal memuat data CRM (Status: ${response.status})`);
          }

          const data = await response.json();
          setCrmData(data.crmData);

          // Ambil nama outlet jika perlu
          if (selectedOutletId) {
            const outletResponse = await fetch(`/api/outlets/${selectedOutletId}`);
            if (outletResponse.ok) {
              const outletData = await outletResponse.json();
              setOutletName(outletData.outlet?.name || 'Outlet Tidak Diketahui');
            }
          }
        } catch (error) {
          setError(error instanceof Error ? error.message : 'Terjadi kesalahan saat memuat data CRM');
          setCrmData(null);
        } finally {
          setIsLoading(false);
        }
      };

      fetchCRMData();
    }
  }, [selectedOutletId, filteredOutletId, startDate, endDate]);

  // --- RFM Calculation Functions ---
  const calculateRFMScores = (customers: Customer[], currentDate: Date): Customer[] => {
    return customers.map(customer => {
      // Recency Score
      const lastVisitDate = new Date(customer.lastVisit);
      const daysSinceLastVisit = differenceInDays(currentDate, lastVisitDate);
      let rScore = 1;
      if (daysSinceLastVisit <= 7) rScore = 5;
      else if (daysSinceLastVisit <= 30) rScore = 4;
      else if (daysSinceLastVisit <= 90) rScore = 3;
      else if (daysSinceLastVisit <= 180) rScore = 2;

      // Frequency Score
      let fScore = 1;
      if (customer.totalVisits >= 10) fScore = 5;
      else if (customer.totalVisits >= 6) fScore = 4;
      else if (customer.totalVisits >= 3) fScore = 3;
      else if (customer.totalVisits >= 2) fScore = 2;

      // Monetary Score (Contoh rentang, bisa disesuaikan)
      let mScore = 1;
      if (customer.totalSpent > 2000000) mScore = 5;
      else if (customer.totalSpent > 1000000) mScore = 4;
      else if (customer.totalSpent > 500000) mScore = 3;
      else if (customer.totalSpent > 100000) mScore = 2;

      return { ...customer, rScore, fScore, mScore };
    });
  };

  const assignRFMSegments = (customers: Customer[]): Customer[] => {
    return customers.map(customer => {
      const { rScore = 0, fScore = 0, mScore = 0 } = customer;
      let rfmSegment = "Reguler";

      if (rScore >= 4 && fScore >= 4 && mScore >= 4) rfmSegment = "Bintang 5 ⭐⭐⭐⭐⭐";
      else if (rScore >= 3 && fScore >= 3 && mScore >= 3) rfmSegment = "Pelanggan Setia 👍";
      else if (rScore >= 4 && fScore <= 2 && mScore <= 2) rfmSegment = "Pelanggan Baru ✨";
      else if (rScore <= 2 && fScore >= 3 && mScore >= 3) rfmSegment = "Berisiko Pergi (Nilai Tinggi) 😟";
      else if (rScore <= 2 && fScore <= 2 && mScore <= 2 && !(rScore === 1 && fScore === 1 && mScore === 1) ) rfmSegment = "Kurang Aktif 📉";
      else if (rScore === 1 && fScore === 1 && mScore === 1) rfmSegment = "Hilang 👻";
      else if (rScore <= 2 && (fScore >= 3 || mScore >= 3)) rfmSegment = "Butuh Perhatian (Nilai Tinggi) ⚠️";
      else if ((rScore >= 3 || fScore >=3 || mScore >=3) && (rScore <=2 || fScore <=2 || mScore <=2) ) rfmSegment = "Pelanggan Potensial 🌱";


      return { ...customer, rfmSegment };
    });
  };

  // Efek untuk memproses data CRM dengan skor RFM
  useEffect(() => {
    if (crmData) {
      const customersWithScores = calculateRFMScores(crmData.customers, endDate); // Gunakan endDate sebagai referensi 'hari ini'
      const customersWithSegments = assignRFMSegments(customersWithScores);
      setProcessedCrmData({
        ...crmData,
        customers: customersWithSegments,
      });
    } else {
      setProcessedCrmData(null);
    }
  }, [crmData, endDate]); // Tambahkan endDate sebagai dependensi

  // Fungsi untuk mengunduh data dalam format Excel
  const handleDownloadExcel = () => {
    if (!processedCrmData) return; // Gunakan processedCrmData

    try {
      // Persiapkan data untuk Excel
      const workbook = XLSX.utils.book_new();

      // Sheet 1: Kategori Pelanggan per Outlet
      const categoryData = processedCrmData.customerCategories.map(outlet => ({
        'Outlet': outlet.outletName,
        'Kategori A': outlet.categoryA,
        'Kategori B': outlet.categoryB,
        'Kategori C': outlet.categoryC,
        'Kategori D': outlet.categoryD,
        'Total': outlet.total
      }));
      const categorySheet = XLSX.utils.json_to_sheet(categoryData);
      XLSX.utils.book_append_sheet(workbook, categorySheet, 'Kategori Pelanggan');

      // Sheet 2: Jam Sibuk dengan Gender
      const peakHoursData = processedCrmData.peakHours.map(hour => ({
        'Jam': `${hour.hour}:00`,
        'Total': hour.count,
        'Pria': hour.maleCount,
        'Wanita': hour.femaleCount,
        'Lainnya': hour.otherCount
      }));
      const peakHoursSheet = XLSX.utils.json_to_sheet(peakHoursData);
      XLSX.utils.book_append_sheet(workbook, peakHoursSheet, 'Jam Sibuk');

      // Sheet 3: Detail Pelanggan (Tambahkan kolom RFM dan tag)
      const customersData = processedCrmData.customers.map(customer => ({
        'Nama': customer.name,
        'Tag': customer.tags && customer.tags.length > 0 ? customer.tags.join(', ') : '',
        'Gender': customer.gender === 'MALE' ? 'Pria' : customer.gender === 'FEMALE' ? 'Wanita' : 'Lainnya',
        'Kategori Asli': customer.category, // Kategori asli jika masih ingin ditampilkan
        'R Score': customer.rScore,
        'F Score': customer.fScore,
        'M Score': customer.mScore,
        'Segmen RFM': customer.rfmSegment,
        'Total Kunjungan': customer.totalVisits,
        'Kunjungan Dalam Periode': customer.visitsInRange || 0,
        'Total Pengeluaran': formatCurrency(customer.totalSpent), // Format sebagai currency
        'Pengeluaran Dalam Periode': formatCurrency(customer.spentInRange || 0),
        'Kunjungan Terakhir': new Date(customer.lastVisit).toLocaleDateString('id-ID'),
        'Outlet Terakhir': formatOutletName(customer.outletName)
      }));
      const customersSheet = XLSX.utils.json_to_sheet(customersData);
      XLSX.utils.book_append_sheet(workbook, customersSheet, 'Detail Pelanggan RFM');

      // Ekspor ke file Excel
      const fileName = `crm-report_${format(new Date(), 'yyyy-MM-dd')}.xlsx`;
      XLSX.writeFile(workbook, fileName);
      toast.success('Data berhasil diunduh dalam format Excel');
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast.error('Gagal mengunduh data Excel. Silakan coba lagi.');
    }
  };

  // Fungsi untuk mengubah filter outlet
  const handleOutletFilterChange = (outletId: string) => {
    setFilteredOutletId(outletId === 'all' ? null : outletId);
  };

  // Fungsi untuk format mata uang
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value).replace('Rp\u00A0', 'Rp');
  };

  // Fungsi untuk memformat nama outlet (menghapus "Breaktime" dan "Palu" dari nama)
  const formatOutletName = (name: string): string => {
    // Hapus "Breaktime", "Palu", dan kata-kata lain yang tidak diinginkan
    return name
      .replace(/breaktime\s+/i, '') // Hapus "Breaktime " (case insensitive)
      .replace(/^breaktime$/i, '') // Hapus jika hanya "Breaktime"
      .replace(/palu\s+/i, '') // Hapus "Palu " (case insensitive)
      .replace(/\s+palu$/i, '') // Hapus " Palu" di akhir
      .replace(/^palu$/i, '') // Hapus jika hanya "Palu"
      .trim();
  };

  // Helper warna badge untuk tag pelanggan (sama dengan di halaman pelanggan)
  const getStatusBadgeStyle = (status: string | undefined | null): string => {
    if (!status) return 'badge-outline'; // Handle undefined or null status
    const lowerStatus = status.toLowerCase();

    // Mapping tag ke style badge
    if (lowerStatus === 'a') return 'badge-success'; // Tag A: Warna hijau (pelanggan rutin)
    if (lowerStatus === 'b') return 'badge-info'; // Tag B: Warna biru (kembali dalam 31-60 hari)
    if (lowerStatus === 'c') return 'badge-warning'; // Tag C: Warna kuning (kembali dalam 61-180 hari)
    if (lowerStatus === 'd') return 'badge-error'; // Tag D: Warna merah (kembali setelah >180 hari)
    if (lowerStatus === 'baru') return 'badge-info'; // Tag Baru: Warna biru muda

    // Tag lainnya
    if (lowerStatus === 'vip') return 'badge-error'; // VIP: Warna merah
    if (lowerStatus === 'loyal') return 'badge-success'; // Loyal: Warna hijau

    // Default untuk tag yang tidak dikenal
    return 'badge-outline';
  };

  // Fungsi untuk menampilkan modal dengan daftar pelanggan berdasarkan kategori
  const handleShowCustomersByCategory = (categoryClicked: 'A' | 'B' | 'C' | 'D', outletId: string | null) => {
    if (!processedCrmData) return;

    let potentialCustomers = processedCrmData.customers;

    // Filter berdasarkan outlet jika outletId tidak null
    if (outletId) {
      potentialCustomers = potentialCustomers.filter(customer => customer.outletId === outletId);
    }

    let customersToShow: Customer[];

    // Logika baru berdasarkan finalCategory
    customersToShow = potentialCustomers.filter(customer => customer.finalCategory === categoryClicked);

    // Urutkan pelanggan berdasarkan total pengeluaran (dari tertinggi ke terendah)
    customersToShow.sort((a, b) => b.totalSpent - a.totalSpent);

    // Set state untuk modal
    setSelectedCategory(categoryClicked);
    setSelectedOutletForModal(outletId);
    setFilteredCustomers(customersToShow);
    setShowCustomerModal(true);
  };

  // Fungsi untuk menutup modal
  const handleCloseModal = () => {
    setShowCustomerModal(false);
    setSelectedCategory(null);
    setSelectedOutletForModal(null);
    setFilteredCustomers([]);
  };

  // Fungsi untuk membuka modal analisis gender
  const handleShowGenderAnalysis = () => {
    setShowGenderAnalysisModal(true);
  };

  // Fungsi untuk menutup modal analisis gender
  const handleCloseGenderAnalysisModal = () => {
    setShowGenderAnalysisModal(false);
  };

  if (isLoading) {
    return <div className="flex justify-center items-center h-screen"><span className="loading loading-spinner loading-lg text-primary"></span></div>;
  }

  if (error) {
    return (
      <div className="flex flex-col justify-center items-center h-screen text-center px-4">
        <FiAlertTriangle className="w-16 h-16 text-error mb-4" />
        <h2 className="text-xl font-semibold text-error mb-2">Terjadi Kesalahan</h2>
        <p className="text-gray-600 mb-6">{error}</p>
        <button type="button" onClick={() => window.location.reload()} className="btn btn-primary">Muat Ulang Halaman</button>
      </div>
    );
  }

  if (!processedCrmData) {
    return <div className="flex justify-center items-center h-screen"><p>Data tidak tersedia.</p></div>;
  }

  // Warna untuk kategori pelanggan
  const categoryColors = {
    A: '#4CAF50', // Hijau
    B: '#2196F3', // Biru
    C: '#FFC107', // Kuning
    D: '#F44336'  // Merah
  };

  // Warna untuk gender
  const genderColors = {
    male: '#2196F3',   // Biru
    female: '#E91E63', // Pink
    other: '#9C27B0'   // Ungu
  };

  // Persiapkan data untuk line chart jam sibuk
  const prepareHourlyData = () => {
    // Buat array dengan semua jam dari 9:00 sampai 23:00
    const hours = Array.from({ length: 15 }, (_, i) => i + 9);

    // Buat objek untuk menyimpan data per jam
    const hourlyData = hours.map(hour => ({
      hour,
      hourLabel: `${hour}:00`,
      maleCount: 0,
      femaleCount: 0,
      otherCount: 0,
      totalCount: 0
    }));

    // Isi data dari crmData.peakHours
    if (processedCrmData && processedCrmData.peakHours) {
      processedCrmData.peakHours.forEach(peak => {
        // Hanya proses jam yang ada dalam range 9-23
        if (peak.hour >= 9 && peak.hour <= 23) {
          const index = peak.hour - 9;
          if (index >= 0 && index < hourlyData.length) {
            hourlyData[index].maleCount = peak.maleCount;
            hourlyData[index].femaleCount = peak.femaleCount;
            hourlyData[index].otherCount = peak.otherCount;
            hourlyData[index].totalCount = peak.count;
          }
        }
      });
    }

    // Pastikan data diurutkan berdasarkan jam
    return hourlyData.sort((a, b) => a.hour - b.hour);
  };

  // Data untuk line chart
  const hourlyData = prepareHourlyData();

  return (
    <motion.div
      className="container mx-auto"
      initial="hidden"
      animate="visible"
      variants={staggerContainer}
    >
      {/* Header */}
      <motion.div variants={fadeInUp} className="mb-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-800 mb-2">Customer Relationship Management</h1>
            <p className="text-gray-600">Analisis pelanggan dan jam sibuk berdasarkan outlet</p>
            <div className="flex items-center gap-1 mt-1 text-xs text-gray-500">
              <FiUser size={12} />
              <span>Akurasi gender telah ditingkatkan menggunakan Genderize.io API</span>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 mt-4 md:mt-0">
            <button
              type="button"
              onClick={() => {
                setIsLoading(true);
                // Reload data dengan parameter yang sama
                const startDateStr = format(startDate, 'yyyy-MM-dd');
                const endDateStr = format(endDate, 'yyyy-MM-dd');
                const outletIdParam = filteredOutletId || 'all';

                fetch(`/api/reports/crm?startDate=${startDateStr}&endDate=${endDateStr}&outletId=${outletIdParam}`)
                  .then(response => {
                    if (!response.ok) {
                      throw new Error(`Gagal memuat data CRM (Status: ${response.status})`);
                    }
                    return response.json();
                  })
                  .then(data => {
                    setCrmData(data.crmData);
                    toast.success('Data CRM berhasil diperbarui');
                  })
                  .catch(error => {
                    setError(error instanceof Error ? error.message : 'Terjadi kesalahan');
                    toast.error('Gagal memperbarui data CRM');
                  })
                  .finally(() => {
                    setIsLoading(false);
                  });
              }}
              className="btn btn-outline btn-sm gap-1"
            >
              <FiRefreshCw className="h-4 w-4" /> Refresh Data
            </button>
            <Link href="/dashboard/reports" className="btn btn-outline btn-sm gap-1">
              <FiChevronLeft className="h-4 w-4" /> Kembali ke Laporan
            </Link>
            <button
              type="button"
              onClick={handleDownloadExcel}
              className="btn btn-primary btn-sm gap-1"
            >
              <FiDownload className="h-4 w-4" /> Unduh Excel
            </button>
          </div>
        </div>
      </motion.div>

      {/* Filter Section */}
      <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow-sm mb-6 border border-gray-200">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="form-control w-full md:w-1/3">
            <label className="label">
              <span className="label-text flex items-center"><FiMapPin className="mr-1" /> Outlet</span>
            </label>
            <select
              className="select select-bordered w-full"
              value={filteredOutletId || 'all'}
              onChange={(e) => handleOutletFilterChange(e.target.value)}
              aria-label="Pilih Outlet"
              title="Pilih Outlet"
            >
              <option value="all">Semua Outlet</option>
              {outlets.map(outlet => (
                <option key={outlet.id} value={outlet.id}>{outlet.name}</option>
              ))}
            </select>
          </div>
          <div className="form-control w-full md:w-1/3">
            <label className="label">
              <span className="label-text flex items-center"><FiCalendar className="mr-1" /> Tanggal Mulai</span>
            </label>
            <div className="dropdown dropdown-end w-full">
              <div tabIndex={0} role="button" className="btn btn-outline w-full justify-between">
                {format(startDate, 'dd MMMM yyyy', { locale: idLocale })}
                <FiCalendar className="ml-2" />
              </div>
              <div tabIndex={0} className="dropdown-content z-[1] card card-compact shadow bg-base-100 rounded-box w-64">
                <div className="card-body p-2">
                  <div className="w-full max-w-[300px] mx-auto">
                    <div className="flex justify-between items-center py-2 mb-2">
                      <button
                        type="button"
                        className="btn btn-sm btn-ghost"
                        onClick={(e) => {
                          e.preventDefault();
                          const currentDate = new Date(startDate);
                          currentDate.setMonth(currentDate.getMonth() - 1);
                          setStartDate(currentDate);
                        }}
                      >
                        «
                      </button>
                      <div className="text-sm font-medium">
                        {format(startDate, 'MMMM yyyy', { locale: idLocale })}
                      </div>
                      <button
                        type="button"
                        className="btn btn-sm btn-ghost"
                        onClick={(e) => {
                          e.preventDefault();
                          const currentDate = new Date(startDate);
                          currentDate.setMonth(currentDate.getMonth() + 1);
                          setStartDate(currentDate);
                        }}
                      >
                        »
                      </button>
                    </div>
                    <div className="grid grid-cols-7 gap-1">
                      {/* Hari dalam seminggu */}
                      {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                        <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                      ))}

                      {/* Tanggal */}
                      {Array.from({ length: 42 }, (_, i) => {
                        const currentDate = new Date(startDate);
                        const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
                        const startingDayOfWeek = firstDayOfMonth.getDay();
                        const day = i - startingDayOfWeek + 1;
                        const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
                        const isCurrentMonth = date.getMonth() === currentDate.getMonth();
                        const isSelected = date.getDate() === startDate.getDate() &&
                                          date.getMonth() === startDate.getMonth() &&
                                          date.getFullYear() === startDate.getFullYear();

                        return (
                          <button
                            type="button"
                            key={i}
                            className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all
                                      ${isCurrentMonth ? '' : 'text-gray-300'}
                                      ${isSelected ? 'bg-primary text-primary-content' : ''}
                                      ${isCurrentMonth && !isSelected ? 'hover:bg-base-200' : ''}`}
                            onClick={(e) => {
                              e.preventDefault();
                              const newDate = new Date(startDate);
                              newDate.setDate(date.getDate());
                              newDate.setMonth(date.getMonth());
                              newDate.setFullYear(date.getFullYear());
                              setStartDate(newDate);
                            }}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="form-control w-full md:w-1/3">
            <label className="label">
              <span className="label-text flex items-center"><FiCalendar className="mr-1" /> Tanggal Akhir</span>
            </label>
            <div className="dropdown dropdown-end w-full">
              <div tabIndex={0} role="button" className="btn btn-outline w-full justify-between">
                {format(endDate, 'dd MMMM yyyy', { locale: idLocale })}
                <FiCalendar className="ml-2" />
              </div>
              <div tabIndex={0} className="dropdown-content z-[1] card card-compact shadow bg-base-100 rounded-box w-64">
                <div className="card-body p-2">
                  <div className="w-full max-w-[300px] mx-auto">
                    <div className="flex justify-between items-center py-2 mb-2">
                      <button
                        type="button"
                        className="btn btn-sm btn-ghost"
                        onClick={(e) => {
                          e.preventDefault();
                          const currentDate = new Date(endDate);
                          currentDate.setMonth(currentDate.getMonth() - 1);
                          setEndDate(currentDate);
                        }}
                      >
                        «
                      </button>
                      <div className="text-sm font-medium">
                        {format(endDate, 'MMMM yyyy', { locale: idLocale })}
                      </div>
                      <button
                        type="button"
                        className="btn btn-sm btn-ghost"
                        onClick={(e) => {
                          e.preventDefault();
                          const currentDate = new Date(endDate);
                          currentDate.setMonth(currentDate.getMonth() + 1);
                          setEndDate(currentDate);
                        }}
                      >
                        »
                      </button>
                    </div>
                    <div className="grid grid-cols-7 gap-1">
                      {/* Hari dalam seminggu */}
                      {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                        <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                      ))}

                      {/* Tanggal */}
                      {Array.from({ length: 42 }, (_, i) => {
                        const currentDate = new Date(endDate);
                        const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
                        const startingDayOfWeek = firstDayOfMonth.getDay();
                        const day = i - startingDayOfWeek + 1;
                        const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
                        const isCurrentMonth = date.getMonth() === currentDate.getMonth();
                        const isSelected = date.getDate() === endDate.getDate() &&
                                          date.getMonth() === endDate.getMonth() &&
                                          date.getFullYear() === endDate.getFullYear();

                        return (
                          <button
                            type="button"
                            key={i}
                            className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all
                                      ${isCurrentMonth ? '' : 'text-gray-300'}
                                      ${isSelected ? 'bg-primary text-primary-content' : ''}
                                      ${isCurrentMonth && !isSelected ? 'hover:bg-base-200' : ''}`}
                            onClick={(e) => {
                              e.preventDefault();
                              const newDate = new Date(endDate);
                              newDate.setDate(date.getDate());
                              newDate.setMonth(date.getMonth());
                              newDate.setFullYear(date.getFullYear());
                              setEndDate(newDate);
                            }}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-end mt-4">
          <button
            type="button"
            onClick={handleShowGenderAnalysis}
            className="btn btn-sm btn-outline gap-1"
          >
            <FiUser className="h-4 w-4" /> Analisis Gender Pelanggan
          </button>
        </div>
      </motion.div>

      {/* Kategori Pelanggan per Outlet */}
      <motion.div variants={fadeInUp} className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <h2 className="text-lg font-semibold mb-4">Kategori Pelanggan per Outlet</h2>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={processedCrmData.customerCategories}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="outletName"
                  tickFormatter={(name) => formatOutletName(name)}
                />
                <YAxis />
                <Tooltip
                  formatter={(value, name, props) => [value, name]}
                  labelFormatter={(label) => formatOutletName(label)}
                />
                <Legend />
                <Bar dataKey="categoryA" name="Kategori A" fill={categoryColors.A} />
                <Bar dataKey="categoryB" name="Kategori B" fill={categoryColors.B} />
                <Bar dataKey="categoryC" name="Kategori C" fill={categoryColors.C} />
                <Bar dataKey="categoryD" name="Kategori D" fill={categoryColors.D} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Jam Sibuk dengan Gender */}
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
          <h2 className="text-lg font-semibold mb-4">Jam Sibuk berdasarkan Gender (WITA)</h2>

          {/* Tabs untuk memilih jenis visualisasi */}
          <div className="tabs tabs-boxed bg-gray-100 mb-4">
            <button
              type="button"
              className={`tab ${activeChartTab === 'bar' ? 'tab-active' : ''}`}
              onClick={() => setActiveChartTab('bar')}
            >
              Bar Chart
            </button>
            <button
              type="button"
              className={`tab ${activeChartTab === 'line' ? 'tab-active' : ''}`}
              onClick={() => setActiveChartTab('line')}
            >
              Line Chart
            </button>
          </div>

          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              {activeChartTab === 'bar' ? (
                <BarChart
                  data={[...processedCrmData.peakHours].sort((a, b) => a.hour - b.hour)} // Urutkan berdasarkan jam
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="hour"
                    tickFormatter={(hour) => `${hour}:00`}
                    label={{ value: 'Jam (WITA)', position: 'insideBottomRight', offset: -10 }}
                  />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="maleCount" name="Pria" fill={genderColors.male} />
                  <Bar dataKey="femaleCount" name="Wanita" fill={genderColors.female} />
                  <Bar dataKey="otherCount" name="Lainnya" fill={genderColors.other} />
                </BarChart>
              ) : (
                <LineChart
                  data={[...processedCrmData.peakHours].sort((a, b) => a.hour - b.hour)} // Urutkan berdasarkan jam
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="hour"
                    tickFormatter={(hour) => `${hour}:00`}
                    label={{ value: 'Jam (WITA)', position: 'insideBottomRight', offset: -10 }}
                  />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="count"
                    name="Total"
                    stroke="#8884d8"
                    strokeWidth={2}
                    activeDot={{ r: 8 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="maleCount"
                    name="Pria"
                    stroke={genderColors.male}
                    strokeWidth={2}
                  />
                  <Line
                    type="monotone"
                    dataKey="femaleCount"
                    name="Wanita"
                    stroke={genderColors.female}
                    strokeWidth={2}
                  />
                  <Line
                    type="monotone"
                    dataKey="otherCount"
                    name="Lainnya"
                    stroke={genderColors.other}
                    strokeWidth={1}
                    strokeDasharray="5 5"
                  />
                </LineChart>
              )}
            </ResponsiveContainer>
          </div>
        </div>

        {/* Line Chart Jam Sibuk (09:00-23:00) */}
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 lg:col-span-2">
          <h2 className="text-lg font-semibold mb-4">Tren Kunjungan berdasarkan Jam Operasional (09:00-23:00 WITA)</h2>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={hourlyData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="hourLabel"
                  label={{ value: 'Jam (WITA)', position: 'insideBottomRight', offset: -10 }}
                />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="totalCount"
                  name="Total Kunjungan"
                  stroke="#8884d8"
                  strokeWidth={2}
                  activeDot={{ r: 8 }}
                />
                <Line
                  type="monotone"
                  dataKey="maleCount"
                  name="Pria"
                  stroke={genderColors.male}
                  strokeWidth={2}
                />
                <Line
                  type="monotone"
                  dataKey="femaleCount"
                  name="Wanita"
                  stroke={genderColors.female}
                  strokeWidth={2}
                />
                <Line
                  type="monotone"
                  dataKey="otherCount"
                  name="Lainnya"
                  stroke={genderColors.other}
                  strokeWidth={1}
                  strokeDasharray="5 5"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </motion.div>

      {/* Tabel Detail Kategori Pelanggan */}
      <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow-sm mb-6 border border-gray-200">
        <h2 className="text-lg font-semibold mb-4">Detail Kategori Pelanggan per Outlet</h2>
        <div className="mb-2 text-sm text-gray-500 italic">
          <div className="flex items-center gap-1">
            <FiInfo size={14} />
            <span>Data menampilkan semua pelanggan yang pernah bertransaksi di masing-masing outlet</span>
          </div>
          <div className="flex items-center gap-1 mt-1">
            <FiInfo size={14} />
            <span>Satu pelanggan dapat dihitung di beberapa outlet jika pernah bertransaksi di outlet tersebut</span>
          </div>
          <div className="flex items-center gap-1 mt-1">
            <FiInfo size={14} />
            <span>
              Ktg. A: Rutin (transaksi berulang ≤30 hari, min. 2x) | 
              Ktg. B: Kembali 31-60 hr (min. 2x total trx) | 
              Ktg. C: Kembali 61-180 hr (min. 2x total trx) | 
              Ktg. D: Lainnya/Baru 1x
            </span>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="table table-zebra w-full">
            <thead>
              <tr>
                <th>Outlet</th>
                <th>
                  <span className={`badge ${getStatusBadgeStyle('A')}`}>Kategori A</span>
                </th>
                <th>
                  <span className={`badge ${getStatusBadgeStyle('B')}`}>Kategori B</span>
                </th>
                <th>
                  <span className={`badge ${getStatusBadgeStyle('C')}`}>Kategori C</span>
                </th>
                <th>
                  <span className={`badge ${getStatusBadgeStyle('D')}`}>Kategori D</span>
                </th>
                <th>Total</th>
                <th>Aksi</th>
              </tr>
            </thead>
            <tbody>
              {processedCrmData.customerCategories.map((outlet, index) => (
                <tr key={index}>
                  <td>{formatOutletName(outlet.outletName)}</td>
                  <td>
                    <div className="flex items-center gap-2">
                      <span className={`badge ${getStatusBadgeStyle('A')}`}>{outlet.categoryA}</span>
                      {outlet.categoryA > 0 && (
                        <button
                          type="button"
                          className="btn btn-xs btn-ghost text-primary"
                          onClick={() => handleShowCustomersByCategory('A', outlet.outletId)}
                          title="Lihat pelanggan kategori A"
                        >
                          <FiEye size={14} />
                        </button>
                      )}
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center gap-2">
                      <span className={`badge ${getStatusBadgeStyle('B')}`}>{outlet.categoryB}</span>
                      {outlet.categoryB > 0 && (
                        <button
                          type="button"
                          className="btn btn-xs btn-ghost text-primary"
                          onClick={() => handleShowCustomersByCategory('B', outlet.outletId)}
                          title="Lihat pelanggan kategori B"
                        >
                          <FiEye size={14} />
                        </button>
                      )}
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center gap-2">
                      <span className={`badge ${getStatusBadgeStyle('C')}`}>{outlet.categoryC}</span>
                      {outlet.categoryC > 0 && (
                        <button
                          type="button"
                          className="btn btn-xs btn-ghost text-primary"
                          onClick={() => handleShowCustomersByCategory('C', outlet.outletId)}
                          title="Lihat pelanggan kategori C"
                        >
                          <FiEye size={14} />
                        </button>
                      )}
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center gap-2">
                      <span className={`badge ${getStatusBadgeStyle('D')}`}>{outlet.categoryD}</span>
                      {outlet.categoryD > 0 && (
                        <button
                          type="button"
                          className="btn btn-xs btn-ghost text-primary"
                          onClick={() => handleShowCustomersByCategory('D', outlet.outletId)}
                          title="Lihat pelanggan kategori D"
                        >
                          <FiEye size={14} />
                        </button>
                      )}
                    </div>
                  </td>
                  <td className="font-semibold">{outlet.total}</td>
                  <td>
                    <div className="flex gap-1">
                      <button
                        type="button"
                        className="btn btn-xs btn-outline btn-primary"
                        onClick={() => {
                          // Tampilkan semua pelanggan dari outlet ini
                          const allCustomers = processedCrmData.customers.filter(c => c.outletId === outlet.outletId);
                          setFilteredCustomers(allCustomers);
                          setSelectedOutletForModal(outlet.outletId);
                          setSelectedCategory(null);
                          setShowCustomerModal(true);
                        }}
                      >
                        Semua Pelanggan
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </motion.div>

      {/* Modal untuk menampilkan daftar pelanggan */}
      {showCustomerModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
            <div className="p-4 border-b flex justify-between items-center">
              <div>
                <h3 className="text-lg font-semibold">
                  {selectedCategory
                    ? `Pelanggan Kategori ${selectedCategory}${selectedOutletForModal ? ' - ' + formatOutletName(outlets.find(o => o.id === selectedOutletForModal)?.name || '') : ''}`
                    : `Semua Pelanggan${selectedOutletForModal ? ' - ' + formatOutletName(outlets.find(o => o.id === selectedOutletForModal)?.name || '') : ''}`
                  }
                </h3>
                <div className="text-xs text-gray-500 mt-1 flex items-center gap-1">
                  <span className="badge badge-xs badge-info mr-1">AI</span>
                  <span>Data gender ditingkatkan dengan Genderize.io</span>
                </div>
              </div>
              <button
                type="button"
                className="btn btn-sm btn-ghost"
                onClick={handleCloseModal}
              >
                ×
              </button>
            </div>

            <div className="p-4 overflow-auto flex-grow">
              {filteredCustomers.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">Tidak ada pelanggan yang ditemukan.</p>
                </div>
              ) : (
                <div>
                  <div className="mb-4">
                    <p className="text-sm text-gray-600">Menampilkan {filteredCustomers.length} pelanggan</p>
                    <p className="text-xs text-gray-500 mt-1 flex items-center gap-1">
                      <FiMapPin size={12} className="text-primary" />
                      <span>Kolom Outlet menampilkan lokasi terakhir pelanggan bertransaksi</span>
                    </p>
                    <p className="text-xs text-gray-500 mt-1 flex items-center gap-1">
                      <FiTag size={12} className="text-primary" />
                      <span>Tag pelanggan ditampilkan di bawah nama pelanggan (jika ada)</span>
                    </p>
                    <p className="text-xs text-gray-500 mt-1 flex items-center gap-1">
                      <FiInfo size={12} className="text-primary" />
                      <span>Kategori pelanggan (A/B/C/D) dihitung berdasarkan total kunjungan dan pengeluaran dari semua transaksi</span>
                    </p>
                    <p className="text-xs text-gray-500 mt-1 flex items-center gap-1">
                      <FiUser size={12} className="text-blue-500" />
                      <span>Data gender telah ditingkatkan menggunakan Genderize.io API</span>
                    </p>

                    {/* Gender Stats */}
                    <div className="mt-3 flex flex-wrap gap-3">
                      <div className="stats stats-vertical lg:stats-horizontal shadow bg-base-100">
                        <div className="stat place-items-center">
                          <div className="stat-title text-xs">Pria</div>
                          <div className="stat-value text-primary text-lg flex items-center">
                            <FiUser className="text-blue-500 mr-1" size={14} />
                            {filteredCustomers.filter(c => c.gender === 'MALE').length}
                          </div>
                          <div className="stat-desc text-xs">
                            {Math.round(filteredCustomers.filter(c => c.gender === 'MALE').length / filteredCustomers.length * 100)}%
                          </div>
                        </div>

                        <div className="stat place-items-center">
                          <div className="stat-title text-xs">Wanita</div>
                          <div className="stat-value text-primary text-lg flex items-center">
                            <FiUser className="text-pink-500 mr-1" size={14} />
                            {filteredCustomers.filter(c => c.gender === 'FEMALE').length}
                          </div>
                          <div className="stat-desc text-xs">
                            {Math.round(filteredCustomers.filter(c => c.gender === 'FEMALE').length / filteredCustomers.length * 100)}%
                          </div>
                        </div>

                        <div className="stat place-items-center">
                          <div className="stat-title text-xs">Lainnya</div>
                          <div className="stat-value text-primary text-lg flex items-center">
                            <FiUser className="text-purple-500 mr-1" size={14} />
                            {filteredCustomers.filter(c => c.gender === 'OTHER' || c.gender === null).length}
                          </div>
                          <div className="stat-desc text-xs">
                            {Math.round(filteredCustomers.filter(c => c.gender === 'OTHER' || c.gender === null).length / filteredCustomers.length * 100)}%
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="overflow-x-auto">
                    <table className="table table-zebra w-full">
                      <thead>
                        <tr>
                          <th>Nama & Tag</th>
                          <th>Gender</th>
                          <th>Kategori</th>
                          <th>R</th>
                          <th>F</th>
                          <th>M</th>
                          <th>Segmen RFM</th>
                          <th>Total Kunjungan</th>
                          <th>Total Pengeluaran</th>
                          <th>Kunjungan Terakhir</th>
                          <th>Outlet</th>
                        </tr>
                      </thead>
                      <tbody>
                        {filteredCustomers.map((customer) => (
                          <tr key={customer.id}>
                            <td className="font-medium">
                              <div>
                                {customer.name}
                                {Array.isArray(customer.tags) && customer.tags.length > 0 ? (
                                  <div className="flex flex-wrap gap-1 mt-1">
                                    {customer.tags.map((tag, idx) => (
                                      <span key={idx} className={`badge badge-sm ${getStatusBadgeStyle(tag)}`}>
                                        {tag}
                                      </span>
                                    ))}
                                  </div>
                                ) : null}
                              </div>
                            </td>
                            <td>
                              <div className="flex items-center gap-1">
                                {customer.gender === 'MALE' ? (
                                  <div className="flex flex-col">
                                    <div className="flex items-center">
                                      <FiUser className="text-blue-500" />
                                      <span className="ml-1">Pria</span>
                                    </div>
                                    <div className="tooltip tooltip-right" data-tip="Terverifikasi dengan Genderize.io">
                                      <span className="badge badge-xs badge-info">AI</span>
                                    </div>
                                  </div>
                                ) : customer.gender === 'FEMALE' ? (
                                  <div className="flex flex-col">
                                    <div className="flex items-center">
                                      <FiUser className="text-pink-500" />
                                      <span className="ml-1">Wanita</span>
                                    </div>
                                    <div className="tooltip tooltip-right" data-tip="Terverifikasi dengan Genderize.io">
                                      <span className="badge badge-xs badge-info">AI</span>
                                    </div>
                                  </div>
                                ) : (
                                  <div className="flex flex-col">
                                    <div className="flex items-center">
                                      <FiUser className="text-purple-500" />
                                      <span className="ml-1">Lainnya</span>
                                    </div>
                                    <div className="tooltip tooltip-right" data-tip="Terverifikasi dengan Genderize.io">
                                      <span className="badge badge-xs badge-info">AI</span>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </td>
                            <td>
                              <span
                                className={`badge ${getStatusBadgeStyle(customer.category)}`}
                              >
                                {customer.category}
                              </span>
                            </td>
                            <td>{customer.rScore}</td>
                            <td>{customer.fScore}</td>
                            <td>{customer.mScore}</td>
                            <td>{customer.rfmSegment}</td>
                            <td>
                              <div className="flex flex-col">
                                <span>{customer.totalVisits}</span>
                                {customer.visitsInRange !== undefined && customer.visitsInRange !== customer.totalVisits && (
                                  <span className="text-xs text-gray-500">
                                    ({customer.visitsInRange} dalam periode)
                                  </span>
                                )}
                              </div>
                            </td>
                            <td>
                              <div className="flex flex-col">
                                <span>{formatCurrency(customer.totalSpent)}</span>
                                {customer.spentInRange !== undefined && customer.spentInRange !== customer.totalSpent && (
                                  <span className="text-xs text-gray-500">
                                    ({formatCurrency(customer.spentInRange)} dalam periode)
                                  </span>
                                )}
                              </div>
                            </td>
                            <td>{new Date(customer.lastVisit).toLocaleDateString('id-ID')}</td>
                            <td>
                              <div className="flex items-center gap-1">
                                <FiMapPin className="text-primary" size={12} />
                                {formatOutletName(customer.outletName)}
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>

            <div className="p-4 border-t flex justify-end">
              <button
                type="button"
                className="btn btn-outline"
                onClick={handleCloseModal}
              >
                Tutup
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Gender Analysis */}
      {showGenderAnalysisModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-3xl max-h-[90vh] flex flex-col">
            <div className="p-4 border-b flex justify-between items-center">
              <div>
                <h3 className="text-lg font-semibold">Analisis Gender Pelanggan</h3>
                <div className="text-xs text-gray-500 mt-1 flex items-center gap-1">
                  <span className="badge badge-xs badge-info mr-1">AI</span>
                  <span>Penentuan gender menggunakan Genderize.io API</span>
                </div>
              </div>
              <button
                type="button"
                className="btn btn-sm btn-ghost"
                onClick={handleCloseGenderAnalysisModal}
              >
                ×
              </button>
            </div>

            <div className="p-4 overflow-auto flex-grow">
              <div className="mb-4">
                <p className="text-sm text-gray-600">
                  Sistem menggunakan <a href="https://genderize.io" target="_blank" rel="noopener noreferrer" className="link link-primary">Genderize.io</a> untuk penentuan gender berdasarkan nama dengan akurasi tinggi
                </p>

                <div className="mt-4 stats shadow">
                  <div className="stat">
                    <div className="stat-figure text-primary">
                      <FiUsers className="text-3xl" />
                    </div>
                    <div className="stat-title">Total Pelanggan</div>
                    <div className="stat-value text-primary">{processedCrmData?.customers.length || 0}</div>
                    <div className="stat-desc">Dengan data gender</div>
                  </div>

                  <div className="stat">
                    <div className="stat-figure text-info">
                      <FiUser className="text-3xl text-blue-500" />
                    </div>
                    <div className="stat-title">Pria</div>
                    <div className="stat-value text-info">
                      {processedCrmData?.customers.filter(c => c.gender === 'MALE').length || 0}
                    </div>
                    <div className="stat-desc text-xs">
                      {processedCrmData?.customers.length
                        ? Math.round((processedCrmData.customers.filter(c => c.gender === 'MALE').length / processedCrmData.customers.length) * 100)
                        : 0}%
                    </div>
                  </div>

                  <div className="stat">
                    <div className="stat-figure text-error">
                      <FiUser className="text-3xl text-pink-500" />
                    </div>
                    <div className="stat-title">Wanita</div>
                    <div className="stat-value text-error">
                      {processedCrmData?.customers.filter(c => c.gender === 'FEMALE').length || 0}
                    </div>
                    <div className="stat-desc text-xs">
                      {processedCrmData?.customers.length
                        ? Math.round((processedCrmData.customers.filter(c => c.gender === 'FEMALE').length / processedCrmData.customers.length) * 100)
                        : 0}%
                    </div>
                  </div>
                </div>

                <div className="mt-4">
                  <h4 className="font-medium mb-2">Distribusi Gender Berdasarkan Outlet</h4>
                  <div className="overflow-x-auto">
                    <table className="table table-zebra table-compact w-full">
                      <thead>
                        <tr>
                          <th>Outlet</th>
                          <th>Pria</th>
                          <th>Wanita</th>
                          <th>Lainnya</th>
                          <th>Total</th>
                        </tr>
                      </thead>
                      <tbody>
                        {processedCrmData?.customerCategories.map((outlet, index) => {
                          // Hitung jumlah gender per outlet
                          const maleCount = processedCrmData.customers
                            .filter(c => c.outletId === outlet.outletId && c.gender === 'MALE').length;
                          const femaleCount = processedCrmData.customers
                            .filter(c => c.outletId === outlet.outletId && c.gender === 'FEMALE').length;
                          const otherCount = processedCrmData.customers
                            .filter(c => c.outletId === outlet.outletId && (c.gender === 'OTHER' || c.gender === null)).length;

                          return (
                            <tr key={index}>
                              <td>{formatOutletName(outlet.outletName)}</td>
                              <td>
                                <div className="flex items-center gap-1">
                                  <FiUser className="text-blue-500" size={12} />
                                  <span>{maleCount}</span>
                                  <span className="text-xs text-gray-500">
                                    ({Math.round((maleCount / outlet.total) * 100)}%)
                                  </span>
                                </div>
                              </td>
                              <td>
                                <div className="flex items-center gap-1">
                                  <FiUser className="text-pink-500" size={12} />
                                  <span>{femaleCount}</span>
                                  <span className="text-xs text-gray-500">
                                    ({Math.round((femaleCount / outlet.total) * 100)}%)
                                  </span>
                                </div>
                              </td>
                              <td>
                                <div className="flex items-center gap-1">
                                  <FiUser className="text-purple-500" size={12} />
                                  <span>{otherCount}</span>
                                  <span className="text-xs text-gray-500">
                                    ({Math.round((otherCount / outlet.total) * 100)}%)
                                  </span>
                                </div>
                              </td>
                              <td className="font-medium">{outlet.total}</td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>

            <div className="p-4 border-t flex justify-end">
              <button
                type="button"
                className="btn btn-outline"
                onClick={handleCloseGenderAnalysisModal}
              >
                Tutup
              </button>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
}
