# Pola Sistem

*Dokumen ini menjelaskan arsitektur, keputusan teknis utama, dan pola desain yang digunakan dalam proyek.*

## Gambaran Arsitektur

- **Model**: Fullstack dengan Next.js App Router.
- **Struktur**: Aplikasi monorepo (atau setidaknya terstruktur dalam satu codebase utama) dimana frontend (React/Next.js pages) dan backend (Next.js API Routes) berada bersama.
- **Komponen Utama**:
    - **Frontend**: Dibangun dengan React (Next.js) dan komponen UI dari `src/components` (termasuk `shadcn/ui` kemungkinan besar, berdasarkan struktur umum proyek Next.js modern, dan `daisyui` dari `package.json`). Menggunakan App Router (`src/app`) untuk routing.
    - **Backend**: API dibuat menggunakan Next.js API Routes (`src/app/api`).
    - **Database**: Interaksi database dikelola melalui Prisma ORM.
    - **Autentikasi/Otorisasi**: Ditangani kemungkinan besar melalui middleware (`src/middleware.ts`) dan API routes (`src/app/api/auth`), menggunakan `jsonwebtoken` dan `bcrypt`.

## Keputusan Teknis Utama

- **Framework**: Next.js (App Router) untuk pengembangan fullstack.
- **Bahasa**: TypeScript untuk type safety.
- **Database ORM**: Prisma untuk interaksi database.
- **Styling**: Tailwind CSS (dengan kemungkinan DaisyUI) untuk styling UI.
- **State Management**: Kemungkinan menggunakan React Context (`src/contexts`) atau state management bawaan React/Next.js.

## Pola Desain yang Digunakan

- **API Route Handlers**: Mengikuti pola Next.js untuk mendefinisikan endpoint API.
- **Component-Based UI**: Menggunakan React untuk membangun UI modular.
- **Repository Pattern (Implisit)**: Prisma bertindak sebagai lapisan abstraksi data, mirip dengan pola repository.
- **Middleware**: Untuk menangani request global seperti autentikasi.
- **Custom Hooks**: (`src/hooks`) untuk logika UI yang dapat digunakan kembali.
- **Server Components & Client Components**: Memanfaatkan fitur Next.js App Router.

## Hubungan Antar Komponen

- Komponen Frontend (Client & Server Components) di `src/app` melakukan request ke API Routes di `src/app/api`.
- API Routes menggunakan Prisma Client untuk berinteraksi dengan database.
- Middleware mencegat request untuk tugas seperti autentikasi sebelum mencapai page atau API route.
- Komponen UI dari `src/components` digunakan di seluruh halaman frontend.
- Konteks (`src/contexts`) dan Hooks (`src/hooks`) menyediakan state dan logika bersama untuk komponen frontend. 