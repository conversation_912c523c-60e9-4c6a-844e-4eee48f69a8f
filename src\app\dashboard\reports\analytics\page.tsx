'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  FiCalendar, FiFilter, FiBarChart2, FiTrendingUp,
  FiAlertTriangle, FiRefreshCw, FiChevronLeft
} from 'react-icons/fi';
import { format } from 'date-fns';
import { id as idLocale } from 'date-fns/locale';
import { useOutletContext } from '@/contexts/OutletContext';
import Link from 'next/link';

// Import komponen laporan
import { RevenueChart, ReportCard } from '@/components/reports';

// Format tanggal untuk tampilan
const formatDateDisplay = (date: Date | null): string => {
  if (!date) return '';
  return format(date, 'dd MMMM yyyy', { locale: idLocale });
};

// Format currency
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0
  }).format(value);
};

// Varian Animasi
const fadeInUp = { hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0, transition: { duration: 0.5 } } };
const staggerContainer = { hidden: { opacity: 0 }, visible: { opacity: 1, transition: { staggerChildren: 0.1 } } };

// Interface untuk data analitik
interface AnalyticsData {
  summary: {
    totalRevenue: number;
    comparisonTotalRevenue: number;
    revenueChangePercentage: number | null;
  };
  revenueByOutlet: Array<{
    outletId: string;
    outletName: string;
    revenue: number;
    comparisonRevenue: number;
    changePercentage: number | null;
  }>;
  revenueByPeriod: Array<{
    date: string;
    revenue: number;
  }>;
  comparisonRevenueByPeriod: Array<{
    date: string;
    revenue: number;
  }>;
  revenuePrediction: Array<{
    date: string;
    revenue: number;
  }>;
}

interface Outlet {
  id: string;
  name: string;
}

// Tipe data untuk RevenueChart - revenue harus number
interface RevenueChartItem {
  date: string;
  revenue: number; // Harus number
  previousPeriod?: number;
  prediction?: number;
}

export default function AnalyticsPage() {
  const { selectedOutletId: contextOutletId } = useOutletContext() ?? { selectedOutletId: null };
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [outlets, setOutlets] = useState<Outlet[]>([]);

  // Filters State
  const [startDate, setStartDate] = useState<Date | null>(() => {
    const today = new Date();
    return new Date(today.getFullYear(), today.getMonth(), 1); // Tanggal 1 bulan ini
  });
  const [endDate, setEndDate] = useState<Date | null>(new Date()); // Hari ini
  const [selectedOutletId, setSelectedOutletId] = useState<string>(contextOutletId || '');
  const [period, setPeriod] = useState<string>('daily');
  const [compareWith, setCompareWith] = useState<string>('none');
  const [showPrediction, setShowPrediction] = useState<boolean>(false);

  // Load outlets for filter dropdown
  useEffect(() => {
    const fetchOutlets = async () => {
      try {
        const response = await fetch('/api/outlets?simple=true');
        if (!response.ok) throw new Error('Gagal memuat outlet');
        const data = await response.json();
        setOutlets(data.outlets || []);

        // Hanya set state lokal dari context JIKA state lokal saat ini KOSONG ('')
        // DAN contextOutletId ada DAN valid (ada di daftar outlet).
        if (selectedOutletId === '' && contextOutletId && data.outlets.some((o: Outlet) => o.id === contextOutletId)) {
          setSelectedOutletId(contextOutletId);
        }
      } catch /*(err)*/ {
        // Abaikan error fetching outlets
      }
    };
    fetchOutlets();
  }, [contextOutletId]);

  // Fetch analytics data
  const fetchAnalyticsData = async () => {
    if (!startDate || !endDate) {
      setError("Tanggal mulai dan tanggal selesai harus dipilih.");
      setAnalyticsData(null);
      return;
    }

    setIsLoading(true);
    setError(null);
    setAnalyticsData(null);

    // Format tanggal untuk API (YYYY-MM-DD)
    const formatLocalDate = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    const formattedStartDate = formatLocalDate(startDate);
    const formattedEndDate = formatLocalDate(endDate);

    const params = new URLSearchParams({
      startDate: formattedStartDate,
      endDate: formattedEndDate,
      period,
      compareWith,
    });

    if (selectedOutletId) {
      params.set('outletId', selectedOutletId);
    }

    const apiUrl = `/api/reports/analytics?${params.toString()}`;

    try {
      const response = await fetch(apiUrl);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Gagal memuat data analitik: ${response.statusText}`);
      }

      const data = await response.json();
      setAnalyticsData(data.analytics);
    } catch (err) {
      console.error('Error fetching analytics data:', err);
      setError(err instanceof Error ? err.message : "Terjadi kesalahan saat memuat data analitik.");
    } finally {
      setIsLoading(false);
    }
  };

  // Automatically fetch data when filters change
  useEffect(() => {
    fetchAnalyticsData();
  }, [startDate, endDate, selectedOutletId, period, compareWith]);

  // Prepare chart data (tipe RevenueChartItem)
  const revenueChartData: RevenueChartItem[] = useMemo(() => {
    if (!analyticsData?.revenueByPeriod) return [];
    return analyticsData.revenueByPeriod.map(item => {
      const comparisonItem = analyticsData?.comparisonRevenueByPeriod.find(
        c => new Date(c.date).getTime() === new Date(item.date).getTime()
      );
      return {
        date: item.date,
        revenue: item.revenue,
        previousPeriod: comparisonItem?.revenue,
      };
    });
  }, [analyticsData]);

  // Combine actual data with prediction data (pastikan tipe RevenueChartItem)
  const combinedChartData: RevenueChartItem[] = useMemo(() => {
    const predictionData: RevenueChartItem[] = showPrediction ? analyticsData?.revenuePrediction.map(item => ({
          date: item.date,
          revenue: 0,
          prediction: item.revenue,
        })) || [] : [];
    // Gabungkan dan sort berdasarkan tanggal
    return [...revenueChartData, ...predictionData].sort((a,b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }, [revenueChartData, analyticsData?.revenuePrediction, showPrediction]);

  return (
    <motion.div
      className="container mx-auto p-4 md:p-6 space-y-6"
      initial="hidden"
      animate="visible"
      variants={staggerContainer}
    >
      {/* Header with Back Button */}
      <motion.div variants={fadeInUp} className="flex items-center gap-2">
        <Link href="/dashboard/reports" className="btn btn-sm btn-ghost">
          <FiChevronLeft /> Kembali
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Dashboard Analitik Lanjutan</h1>
          <p className="text-gray-600">Visualisasi tren pendapatan dan performa outlet</p>
        </div>
      </motion.div>

      {/* Filters Section */}
      <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 items-end">
          {/* Tanggal Mulai Dropdown Calendar */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiCalendar className="text-gray-600" /> Tanggal Mulai
            </label>
            <div className="dropdown dropdown-bottom w-full">
              <div tabIndex={0} role="button" className="btn btn-sm btn-outline w-full justify-between overflow-hidden">
                <span className="truncate">{startDate ? formatDateDisplay(startDate) : 'Pilih tanggal'}</span>
                <span className="text-xs">▼</span>
              </div>
              <div tabIndex={0} className="dropdown-content z-[10] card card-compact shadow bg-base-100 rounded-box w-64">
                <div className="card-body p-2">
                  <div className="w-full max-w-[300px] mx-auto">
                    <div className="flex justify-between items-center py-2 mb-2">
                      <button className="btn btn-sm btn-ghost" onClick={() => setStartDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() - 1, 1) : null)} aria-label="Bulan sebelumnya">«</button>
                      <div className="text-sm font-medium">
                        {startDate ? format(startDate, 'MMMM yyyy', { locale: idLocale }) : 'Pilih tanggal'}
                      </div>
                      <button className="btn btn-sm btn-ghost" onClick={() => setStartDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() + 1, 1) : null)} aria-label="Bulan berikutnya">»</button>
                    </div>
                    <div className="grid grid-cols-7 gap-1">
                      {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                        <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                      ))}
                      {startDate && Array.from({ length: 42 }, (_, i) => {
                        const firstDayOfMonth = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
                        const startingDayOfWeek = firstDayOfMonth.getDay();
                        const day = i - startingDayOfWeek + 1;
                        const date = new Date(startDate.getFullYear(), startDate.getMonth(), day);
                        const isCurrentMonth = date.getMonth() === startDate.getMonth();
                        const isSelected = startDate && date.getDate() === startDate.getDate() && date.getMonth() === startDate.getMonth() && date.getFullYear() === startDate.getFullYear();
                        const isToday = new Date().toDateString() === date.toDateString();
                        const isDisabled = !isCurrentMonth || !!(endDate && date > endDate);

                        return (
                          <button
                            key={i}
                            className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all ${isCurrentMonth ? '' : 'text-gray-300'}
                                      ${isSelected ? 'bg-primary text-primary-content' : ''}
                                      ${isToday && !isSelected ? 'border border-primary text-primary' : ''}
                                      ${isCurrentMonth && !isSelected && !isDisabled ? 'hover:bg-base-200' : ''}
                                      ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                            onClick={() => !isDisabled && setStartDate(new Date(date))}
                            disabled={isDisabled}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Tanggal Selesai Dropdown Calendar */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiCalendar className="text-gray-600" /> Tanggal Selesai
            </label>
            <div className="dropdown dropdown-bottom w-full">
              <div tabIndex={0} role="button" className="btn btn-sm btn-outline w-full justify-between overflow-hidden">
                <span className="truncate">{endDate ? formatDateDisplay(endDate) : 'Pilih tanggal'}</span>
                <span className="text-xs">▼</span>
              </div>
              <div tabIndex={0} className="dropdown-content z-[10] card card-compact shadow bg-base-100 rounded-box w-64">
                <div className="card-body p-2">
                  <div className="w-full max-w-[300px] mx-auto">
                    <div className="flex justify-between items-center py-2 mb-2">
                      <button className="btn btn-sm btn-ghost" onClick={() => setEndDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() - 1, 1) : null)} aria-label="Bulan sebelumnya">«</button>
                      <div className="text-sm font-medium">
                        {endDate ? format(endDate, 'MMMM yyyy', { locale: idLocale }) : 'Pilih tanggal'}
                      </div>
                      <button className="btn btn-sm btn-ghost" onClick={() => setEndDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() + 1, 1) : null)} aria-label="Bulan berikutnya">»</button>
                    </div>
                    <div className="grid grid-cols-7 gap-1">
                      {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                        <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                      ))}
                      {endDate && Array.from({ length: 42 }, (_, i) => {
                        const firstDayOfMonth = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
                        const startingDayOfWeek = firstDayOfMonth.getDay();
                        const day = i - startingDayOfWeek + 1;
                        const date = new Date(endDate.getFullYear(), endDate.getMonth(), day);
                        const isCurrentMonth = date.getMonth() === endDate.getMonth();
                        const isSelected = endDate && date.getDate() === endDate.getDate() && date.getMonth() === endDate.getMonth() && date.getFullYear() === endDate.getFullYear();
                        const isToday = new Date().toDateString() === date.toDateString();
                        const isDisabled = !isCurrentMonth || !!(startDate && date < startDate) || date > new Date();

                        return (
                          <button
                            key={i}
                            className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all ${isCurrentMonth ? '' : 'text-gray-300'}
                                      ${isSelected ? 'bg-primary text-primary-content' : ''}
                                      ${isToday && !isSelected ? 'border border-primary text-primary' : ''}
                                      ${isCurrentMonth && !isSelected && !isDisabled ? 'hover:bg-base-200' : ''}
                                      ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                            onClick={() => !isDisabled && setEndDate(new Date(date))}
                            disabled={isDisabled}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Filter Outlet */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiFilter className="text-gray-600" /> Outlet
            </label>
            <select
              className="select select-bordered select-sm w-full"
              value={selectedOutletId}
              onChange={(e) => setSelectedOutletId(e.target.value)}
              aria-label="Outlet Filter"
            >
              <option value="">Semua Outlet</option>
              {outlets.map(outlet => (
                <option key={outlet.id} value={outlet.id}>{outlet.name}</option>
              ))}
            </select>
          </div>

          {/* Filter Periode */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiBarChart2 className="text-gray-600" /> Periode
            </label>
            <select
              className="select select-bordered select-sm w-full"
              value={period}
              onChange={(e) => setPeriod(e.target.value)}
              aria-label="Pilih Periode Agregasi"
            >
              <option value="daily">Harian</option>
              <option value="weekly">Mingguan</option>
              <option value="monthly">Bulanan</option>
            </select>
          </div>

          {/* Filter Perbandingan & Refresh */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiTrendingUp className="text-gray-600" /> Bandingkan / Refresh
            </label>
            <div className="flex gap-2">
              <select
                className="select select-bordered select-sm flex-1"
                value={compareWith}
                onChange={(e) => setCompareWith(e.target.value)}
                aria-label="Bandingkan Dengan Periode"
              >
                <option value="none">Tanpa Perbandingan</option>
                <option value="previous_period">Periode Sebelumnya</option>
                <option value="previous_year">Tahun Sebelumnya</option>
              </select>
              <button
                className="btn btn-sm btn-square btn-outline"
                onClick={fetchAnalyticsData}
                title="Refresh Data"
                disabled={isLoading}
              >
                <FiRefreshCw className={isLoading ? 'animate-spin' : ''}/>
              </button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Loading State */}
      {isLoading && (
        <motion.div variants={fadeInUp} className="text-center py-10">
          <span className="loading loading-spinner loading-lg text-primary"></span>
          <p className="mt-2 text-gray-600">Memuat data analitik...</p>
        </motion.div>
      )}

      {/* Error State */}
      {error && !isLoading && (
        <motion.div variants={fadeInUp} className="alert alert-error">
          <FiAlertTriangle />
          <span>Error: {error}</span>
        </motion.div>
      )}

      {/* Analytics Data Display */}
      {!isLoading && !error && analyticsData && (
        <>
          {/* Summary Cards */}
          <motion.div variants={fadeInUp} className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <ReportCard
              title="Total Pendapatan"
              value={Math.round(analyticsData.summary.totalRevenue)}
              previousValue={compareWith !== 'none' ? Math.round(analyticsData.summary.comparisonTotalRevenue) : undefined}
              type="currency"
              icon="money"
            />
            <ReportCard
              title="Rata-rata Pendapatan Harian"
              value={analyticsData.revenueByPeriod.length > 0
                ? Math.round(analyticsData.summary.totalRevenue / analyticsData.revenueByPeriod.length)
                : 0}
              type="currency"
              icon="calendar"
            />
            <ReportCard
              title="Prediksi Pendapatan 7 Hari Kedepan"
              value={Math.round(analyticsData.revenuePrediction.reduce((sum, item) => sum + item.revenue, 0))}
              type="currency"
              icon="money"
            />
          </motion.div>

          {/* Revenue Chart */}
          <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold flex items-center gap-2 text-gray-700">
                <FiBarChart2 /> Tren Pendapatan
              </h2>
              <div className="form-control">
                <label className="cursor-pointer label">
                  <span className="label-text mr-2">Prediksi</span>
                  <input
                    type="checkbox"
                    className="toggle toggle-sm toggle-primary"
                    checked={showPrediction}
                    onChange={(e) => setShowPrediction(e.target.checked)}
                  />
                </label>
              </div>
            </div>
            <RevenueChart
              data={combinedChartData}
              height={400}
              showComparison={compareWith !== 'none'}
              showPrediction={showPrediction}
            />
          </motion.div>

          {/* Outlet Performance */}
          {analyticsData.revenueByOutlet.length > 1 && (
            <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow">
              <h2 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-700">
                <FiFilter /> Pendapatan per Outlet
              </h2>
              <div className="overflow-x-auto">
                <table className="table table-zebra w-full">
                  <thead>
                    <tr>
                      <th>Outlet</th>
                      <th className="text-right">Pendapatan</th>
                      {compareWith !== 'none' && <th className="text-right">Pendapatan Lalu</th>}
                      {compareWith !== 'none' && <th className="text-right">Perubahan (%)</th>}
                    </tr>
                  </thead>
                  <tbody>
                    {analyticsData.revenueByOutlet.map((outletData) => (
                      <tr key={outletData.outletId}>
                        <td>{outletData.outletName}</td>
                        <td className="text-right">{formatCurrency(outletData.revenue)}</td>
                        {compareWith !== 'none' && <td className="text-right">{formatCurrency(outletData.comparisonRevenue)}</td>}
                        {compareWith !== 'none' && (
                          <td className={`text-right font-medium ${outletData.changePercentage === null ? 'text-gray-500' : outletData.changePercentage >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {outletData.changePercentage === null ? '-' : `${outletData.changePercentage >= 0 ? '+' : ''}${outletData.changePercentage.toFixed(1)}%`}
                          </td>
                        )}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </motion.div>
          )}

          {/* Prediction Insights */}
          {showPrediction && analyticsData.revenuePrediction.length > 0 && (
            <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow">
              <h2 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-700">
                <FiTrendingUp /> Prediksi Pendapatan
              </h2>
              <div className="overflow-x-auto">
                <table className="table table-zebra w-full">
                  <thead>
                    <tr>
                      <th>Tanggal</th>
                      <th className="text-right">Prediksi Pendapatan</th>
                    </tr>
                  </thead>
                  <tbody>
                    {analyticsData.revenuePrediction.map((prediction) => (
                      <tr key={prediction.date}>
                        <td>{format(new Date(prediction.date), 'dd MMMM yyyy', { locale: idLocale })}</td>
                        <td className="text-right">{formatCurrency(prediction.revenue)}</td>
                      </tr>
                    ))}
                    <tr className="font-semibold">
                      <td>Total Prediksi 7 Hari</td>
                      <td className="text-right">{formatCurrency(analyticsData.revenuePrediction.reduce((sum, item) => sum + item.revenue, 0))}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <h3 className="font-semibold text-blue-800">Catatan Prediksi</h3>
                <p className="text-sm text-blue-700 mt-1">
                  Prediksi pendapatan didasarkan pada tren historis dan pola pertumbuhan. Hasil aktual dapat bervariasi tergantung pada faktor eksternal seperti promosi, cuaca, dan peristiwa khusus.
                </p>
              </div>
            </motion.div>
          )}
        </>
      )}

      {/* No Data State */}
      {!isLoading && !error && !analyticsData && (
        <motion.div variants={fadeInUp} className="bg-white p-8 rounded-lg shadow text-center">
          <FiBarChart2 className="mx-auto text-gray-400 mb-4" size={48} />
          <h3 className="text-lg font-medium text-gray-900">Tidak Ada Data Analitik</h3>
          <p className="mt-2 text-gray-500">Tidak ada data pendapatan untuk periode atau filter yang dipilih.</p>
        </motion.div>
      )}
    </motion.div>
  );
}
