'use client';

import React, { useState, useEffect, useMemo, useRef } from 'react';
import { motion } from 'framer-motion';
import {
  FiCalendar, FiFilter, FiBarChart2, FiUsers, FiTrendingUp,
  FiUserCheck, FiClock, FiAlertTriangle, FiFileText, FiGrid, FiLock, FiX,
  FiUser, FiEye
} from 'react-icons/fi';
import { format, parse, isValid, addDays } from 'date-fns';
import { id as idLocale } from 'date-fns/locale';
import { useOutletContext } from '@/contexts/OutletContext';
import { useThemeContext } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import Link from 'next/link';
import {
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  LineChart, Line, PieChart, Pie, Cell, AreaChart, Area
} from 'recharts';

// Import library download
import * as XLSX from 'xlsx';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import * as htmlToImage from 'html-to-image';

// Format tanggal untuk tampilan
const formatDateDisplay = (date: Date | null): string => {
  if (!date) return '';
  return format(date, 'dd MMMM yyyy', { locale: idLocale });
};

// --- Interfaces for Report Data (match API response) ---


interface PopularServiceData {
  serviceId: string;
  serviceName: string;
  count: number;
}

interface TherapistPerformanceData {
  therapistId: string;
  therapistName: string;
  totalSales: number;
  totalCommission: number;
}

interface FrequentCustomerData {
  customerId: string;
  customerName: string;
  customerPhone: string;
  visitCount: number;
  totalSpent: number;
}

interface OutletSalesComparisonData {
  outletId: string;
  outletName: string;
  totalSales: number;
}

interface PeakHourData {
  hour: number;
  count: number;
}

interface DailyRevenueData {
  date: string;
  revenue: number;
}

interface ServiceByHourData {
  id: string;
  name: string;
  count: number;
  totalAmount: number;
}

interface TransactionByHourData {
  id: string;
  displayId: string | null;
  transactionDate: string;
  totalAmount: number;
  customerName: string;
  therapistName: string;
  services: {
    name: string;
    quantity: number;
    price: number;
  }[];
}

interface HourDetailData {
  hour: number;
  transactionCount: number;
  serviceStats: ServiceByHourData[];
  transactions: TransactionByHourData[];
}

interface CashierPerformanceData {
  cashierId: string;
  cashierName: string;
  cashierUsername: string;
  outletId: string;
  outletName: string;
  totalTransactions: number;
  totalAmount: number;
}

interface CashierTransactionData {
  id: string;
  displayId: string | null;
  transactionDate: string;
  totalAmount: number;
  customerName: string;
  therapistName: string;
  paymentMethod: string;
  services: {
    name: string;
    quantity: number;
    price: number;
  }[];
}

interface CashierDetailData {
  cashierName: string;
  outletName: string;
  totalTransactions: number;
  totalAmount: number;
  transactions: CashierTransactionData[];
}

interface ReportData {
  totalRevenue: number;
  popularServices: PopularServiceData[];
  therapistPerformance: TherapistPerformanceData[];
  frequentCustomers: FrequentCustomerData[];
  outletSalesComparison: OutletSalesComparisonData[];
  peakHoursBookings: PeakHourData[];
  peakHoursTransactions: PeakHourData[];
  cashierPerformance?: CashierPerformanceData[]; // Tambahkan data kinerja kasir
}

interface Outlet {
  id: string;
  name: string;
}

// Varian Animasi
const fadeInUp = { hidden: { opacity: 0, y: 20 }, visible: { opacity: 1, y: 0, transition: { duration: 0.5 } } };
const staggerContainer = { hidden: { opacity: 0 }, visible: { opacity: 1, transition: { staggerChildren: 0.1 } } };

// --- Helper Function ---
const formatCurrency = (value: number) => {
   return new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0 }).format(value);
};

// Helper Warna baru berdasarkan Logo (Gold & Teal)
const LOGO_COLORS = ['#FDBA74', '#2DD4BF', '#FCD34D', '#5EEAD4', '#FB923C', '#0D9488']; // Gold/Orange & Teal/Cyan variations

// Helper label Pie Chart
const RADIAN = Math.PI / 180;

// Props type untuk renderCustomizedLabel
interface CustomizedLabelProps {
  cx: number;
  cy: number;
  midAngle: number;
  innerRadius: number;
  outerRadius: number;
  percent: number;
}

// Helper label Pie Chart (perbaiki tipe)
const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: CustomizedLabelProps) => {
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text x={x} y={y} fill="white" textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central" fontSize={10}>
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

// Tidak perlu CSS khusus karena kita akan menggunakan class Tailwind

export default function ReportPage() {
  const { selectedOutletId: contextOutletId } = useOutletContext() ?? { selectedOutletId: null }; // Get outlet from context
  const { theme } = useThemeContext(); // Dapatkan theme dari context
  const { user } = useAuth(); // Dapatkan user dari auth context
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [dailyRevenueData, setDailyRevenueData] = useState<DailyRevenueData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDailyRevenueLoading, setIsDailyRevenueLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dailyRevenueError, setDailyRevenueError] = useState<string | null>(null);
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [allowedOutlets, setAllowedOutlets] = useState<Outlet[]>([]); // Outlet yang diizinkan untuk investor
  const [isInvestor, setIsInvestor] = useState<boolean>(false); // Apakah user adalah investor

  // State untuk modal detail jam sibuk
  const [showHourDetailModal, setShowHourDetailModal] = useState<boolean>(false);
  const [selectedHour, setSelectedHour] = useState<number | null>(null);
  const [hourDetailData, setHourDetailData] = useState<HourDetailData | null>(null);
  const [isHourDetailLoading, setIsHourDetailLoading] = useState<boolean>(false);
  const [hourDetailError, setHourDetailError] = useState<string | null>(null);

  // State untuk modal detail transaksi kasir
  const [showCashierDetailModal, setShowCashierDetailModal] = useState<boolean>(false);
  const [selectedCashier, setSelectedCashier] = useState<CashierPerformanceData | null>(null);
  const [cashierDetailData, setCashierDetailData] = useState<CashierDetailData | null>(null);
  const [isCashierDetailLoading, setIsCashierDetailLoading] = useState<boolean>(false);
  const [cashierDetailError, setCashierDetailError] = useState<string | null>(null);

  // State untuk filter perbandingan
  const [comparisonType, setComparisonType] = useState<string>('none'); // none, weekly, monthly
  const [comparisonData, setComparisonData] = useState<{
    peakHoursBookings: PeakHourData[];
    peakHoursTransactions: PeakHourData[];
  } | null>(null);
  const [isComparisonLoading, setIsComparisonLoading] = useState<boolean>(false);

  // State untuk filter hari dalam seminggu
  const [selectedDay, setSelectedDay] = useState<number | null>(null); // null = semua hari, 0 = Minggu, 1 = Senin, dst
  const [isDayFilterLoading, setIsDayFilterLoading] = useState<boolean>(false);
  const dayNames = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'];
  const [dayFilteredPeakHours, setDayFilteredPeakHours] = useState<{
    peakHoursBookings: PeakHourData[];
    peakHoursTransactions: PeakHourData[];
  } | null>(null);

  // Refs untuk chart
  const dailyRevenueChartRef = useRef<HTMLDivElement>(null);
  const peakHoursChartRef = useRef<HTMLDivElement>(null);
  const outletSalesChartRef = useRef<HTMLDivElement>(null);
  const popularServicesChartRef = useRef<HTMLDivElement>(null);
  const therapistPerformanceChartRef = useRef<HTMLDivElement>(null);

  // --- Filters State ---
  // Default dari tanggal 1 bulan ini hingga hari ini
  const [startDate, setStartDate] = useState<Date | null>(() => {
    const today = new Date();
    return new Date(today.getFullYear(), today.getMonth(), 1); // Tanggal 1 bulan ini
  });
  const [endDate, setEndDate] = useState<Date | null>(new Date()); // Hari ini
  const [selectedOutletId, setSelectedOutletId] = useState<string>(contextOutletId || ''); // Initialize with context or '' for all

  // Cek apakah user adalah investor dan ambil outlet yang diizinkan
  useEffect(() => {
    if (user) {
      // Cek apakah user adalah investor
      setIsInvestor(user.role === 'INVESTOR');

      // Jika user adalah investor, ambil outlet yang diizinkan
      if (user.role === 'INVESTOR') {
        const fetchAllowedOutlets = async () => {
          try {
            const response = await fetch(`/api/users/${user.id}`);
            if (!response.ok) throw new Error('Gagal memuat data user');
            const data = await response.json();

            // Ambil outlet yang diizinkan dari response
            if (data.user && data.user.allowedOutlets) {
              setAllowedOutlets(data.user.allowedOutlets);

              // Jika hanya ada 1 outlet yang diizinkan, set selectedOutletId ke outlet tersebut
              if (data.user.allowedOutlets.length === 1) {
                setSelectedOutletId(data.user.allowedOutlets[0].id);
              } else if (data.user.allowedOutlets.length > 0) {
                // Jika ada lebih dari 1 outlet, cek apakah selectedOutletId ada di daftar outlet yang diizinkan
                const isAllowed = data.user.allowedOutlets.some((o: Outlet) => o.id === selectedOutletId);
                if (!isAllowed) {
                  // Jika tidak, set selectedOutletId ke outlet pertama yang diizinkan
                  setSelectedOutletId(data.user.allowedOutlets[0].id);
                }
              }
            }
          } catch (err) {
            console.error('Error fetching allowed outlets:', err);
          }
        };
        fetchAllowedOutlets();
      }
    }
  }, [user, selectedOutletId]);

  // Load outlets for filter dropdown
  useEffect(() => {
    const fetchOutlets = async () => {
      try {
        const response = await fetch('/api/outlets?simple=true');
        if (!response.ok) throw new Error('Gagal memuat outlet');
        const data = await response.json();
        const fetchedOutlets = data.outlets || [];
        setOutlets(fetchedOutlets);

        // Jika user bukan investor, gunakan logika normal
        if (!isInvestor) {
          // Hanya set state lokal dari context JIKA state lokal saat ini KOSONG ('')
          // DAN contextOutletId ada DAN valid (ada di daftar outlet).
          // Ini mencegah context menimpa pilihan "Semua Outlet" (nilai '') pengguna.
          if (selectedOutletId === '' && contextOutletId && fetchedOutlets.some((o: Outlet) => o.id === contextOutletId)) {
             setSelectedOutletId(contextOutletId);
          }
          // Jika selectedOutletId lokal sudah diisi (termasuk ''), biarkan saja.
        }

      } catch (err) {
        // Abaikan error fetching outlets
      }
    };
    fetchOutlets();
    // Hapus selectedOutletId dari dependency array agar tidak reset saat filter lokal berubah.
    // Efek ini hanya perlu dijalankan saat contextOutletId berubah (atau saat mount awal).
  }, [contextOutletId, isInvestor]);

  // --- Fetch Report Data ---
  const fetchReportData = async () => {
    if (!startDate || !endDate) {
      setError("Tanggal mulai dan tanggal selesai harus dipilih.");
      setReportData(null);
      return;
    }

    setIsLoading(true);
    setError(null);
    setReportData(null);

    // Format tanggal untuk API (YYYY-MM-DD) dengan mempertahankan zona waktu lokal
    // Gunakan format tanggal yang mempertahankan tanggal lokal, bukan UTC
    const formatLocalDate = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    const formattedStartDate = formatLocalDate(startDate);
    const formattedEndDate = formatLocalDate(endDate);

    // Cek apakah tanggal mulai dan akhir adalah hari yang sama
    const isSameDay = formattedStartDate === formattedEndDate;

    const params = new URLSearchParams({
      startDate: formattedStartDate,
      endDate: formattedEndDate,
    });

    if (selectedOutletId) {
      params.set('outletId', selectedOutletId);
    }

    const apiUrl = `/api/reports?${params.toString()}`;

    try {
      const response = await fetch(apiUrl);

      if (!response.ok) {
         const errorData = await response.json();
         throw new Error(errorData.error || `Gagal memuat laporan: ${response.statusText}`);
      }

      const data = await response.json();

      // Ambil data kinerja kasir
      const cashierApiUrl = `/api/reports/cashier-performance?${params.toString()}`;
      const cashierResponse = await fetch(cashierApiUrl);

      if (cashierResponse.ok) {
        const cashierData = await cashierResponse.json();
        // Gabungkan data laporan dengan data kinerja kasir
        setReportData({
          ...data.reports,
          cashierPerformance: cashierData.cashierPerformance || []
        });
      } else {
        // Jika gagal mengambil data kasir, tetap gunakan data laporan utama
        setReportData(data.reports);
        console.error('Error fetching cashier performance data:', cashierResponse.statusText);
      }
    } catch (err) {
      console.error('Error fetching report data:', err);
      setError(err instanceof Error ? err.message : "Terjadi kesalahan saat memuat laporan.");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch daily revenue data
  const fetchDailyRevenueData = async () => {
    if (!startDate || !endDate) {
      setDailyRevenueError("Tanggal mulai dan tanggal selesai harus dipilih.");
      setDailyRevenueData([]);
      return;
    }

    setIsDailyRevenueLoading(true);
    setDailyRevenueError(null);

    // Format tanggal untuk API (YYYY-MM-DD) dengan mempertahankan zona waktu lokal
    // Gunakan format tanggal yang mempertahankan tanggal lokal, bukan UTC
    const formatLocalDate = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    const formattedStartDate = formatLocalDate(startDate);
    const formattedEndDate = formatLocalDate(endDate);

    // Cek apakah tanggal mulai dan akhir adalah hari yang sama
    const isSameDay = formattedStartDate === formattedEndDate;
    console.log(`Daily revenue date check: startDate=${formattedStartDate}, endDate=${formattedEndDate}, isSameDay=${isSameDay}`);

    const params = new URLSearchParams({
      startDate: formattedStartDate,
      endDate: formattedEndDate,
    });

    if (selectedOutletId) {
      params.set('outletId', selectedOutletId);
    }

    const apiUrl = `/api/reports/daily-revenue?${params.toString()}`;
    console.log(`Fetching daily revenue data from: ${apiUrl}`);

    try {
      const response = await fetch(apiUrl);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Gagal memuat data pendapatan harian: ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`Daily revenue data received:`, data.dailyRevenue);
      setDailyRevenueData(data.dailyRevenue);
    } catch (err) {
      console.error('Error fetching daily revenue data:', err);
      setDailyRevenueError(err instanceof Error ? err.message : "Terjadi kesalahan saat memuat data pendapatan harian.");
      setDailyRevenueData([]);
    } finally {
      setIsDailyRevenueLoading(false);
    }
  };

  // Fungsi untuk mengambil detail transaksi berdasarkan jam
  const fetchHourDetail = async (hour: number) => {
    if (!startDate || !endDate) {
      setHourDetailError("Tanggal mulai dan tanggal selesai harus dipilih.");
      return;
    }

    setIsHourDetailLoading(true);
    setHourDetailError(null);
    setHourDetailData(null);

    // Format tanggal untuk API (YYYY-MM-DD)
    const formatLocalDate = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    const formattedStartDate = formatLocalDate(startDate);
    const formattedEndDate = formatLocalDate(endDate);

    const params = new URLSearchParams({
      hour: hour.toString(),
      startDate: formattedStartDate,
      endDate: formattedEndDate,
    });

    if (selectedOutletId) {
      params.set('outletId', selectedOutletId);
    }

    const apiUrl = `/api/reports/transactions-by-hour?${params.toString()}`;

    try {
      const response = await fetch(apiUrl);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Gagal memuat detail jam: ${response.statusText}`);
      }

      const data = await response.json();
      setHourDetailData(data);
      setShowHourDetailModal(true);
    } catch (err) {
      console.error('Error fetching hour detail data:', err);
      setHourDetailError(err instanceof Error ? err.message : "Terjadi kesalahan saat memuat detail jam.");
    } finally {
      setIsHourDetailLoading(false);
    }
  };

  // Fungsi untuk menangani klik pada badge jam sibuk
  const handleHourClick = (hour: number) => {
    setSelectedHour(hour);
    setShowHourDetailModal(true);
    fetchHourDetail(hour);
  };

  // Fetch detail transaksi kasir
  const fetchCashierDetail = async (cashier: CashierPerformanceData) => {
    if (!startDate || !endDate) {
      setCashierDetailError("Tanggal mulai dan tanggal selesai harus dipilih.");
      setCashierDetailData(null);
      return;
    }

    setIsCashierDetailLoading(true);
    setCashierDetailError(null);
    setCashierDetailData(null);

    // Format tanggal untuk API (YYYY-MM-DD)
    const formatLocalDate = (date: Date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    const formattedStartDate = formatLocalDate(startDate);
    const formattedEndDate = formatLocalDate(endDate);

    const params = new URLSearchParams({
      startDate: formattedStartDate,
      endDate: formattedEndDate,
      cashierId: cashier.cashierId,
      outletId: cashier.outletId,
    });

    const apiUrl = `/api/reports/cashier-detail?${params.toString()}`;

    try {
      const response = await fetch(apiUrl);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Gagal memuat detail kasir: ${response.statusText}`);
      }

      const data = await response.json();
      setCashierDetailData(data.cashierDetail);
    } catch (err) {
      console.error('Error fetching cashier detail:', err);
      setCashierDetailError(err instanceof Error ? err.message : "Terjadi kesalahan saat memuat detail kasir.");
    } finally {
      setIsCashierDetailLoading(false);
    }
  };

  // Handle cashier click to show modal
  const handleCashierClick = (cashier: CashierPerformanceData) => {
    setSelectedCashier(cashier);
    setShowCashierDetailModal(true);
    fetchCashierDetail(cashier);
  };

  // Format tanggal untuk API (YYYY-MM-DD)
  const formatLocalDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Fungsi untuk mengambil data berdasarkan hari dalam seminggu
  const fetchDayFilteredData = async () => {
    if (!startDate || !endDate || selectedDay === null) {
      return;
    }

    setIsDayFilterLoading(true);

    // Hitung rentang tanggal untuk filter hari
    const dayFilterStartDate = new Date(startDate.getTime());
    const dayFilterEndDate = new Date(endDate.getTime());

    // Ambil semua tanggal dalam rentang yang memiliki hari yang sama dengan selectedDay
    const allDatesInRange: Date[] = [];
    let currentDate = new Date(dayFilterStartDate.getTime());

    while (currentDate <= dayFilterEndDate) {
      if (currentDate.getDay() === selectedDay) {
        allDatesInRange.push(new Date(currentDate.getTime()));
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    if (allDatesInRange.length === 0) {
      setIsDayFilterLoading(false);
      // Tampilkan pesan error hanya di chart, bukan di seluruh halaman
      return;
    }

    // Ambil data untuk setiap tanggal dan gabungkan
    const allPeakHoursBookings: PeakHourData[] = [];
    const allPeakHoursTransactions: PeakHourData[] = [];

    try {
      for (const date of allDatesInRange) {
        const formattedDate = formatLocalDate(date);

        const params = new URLSearchParams({
          startDate: formattedDate,
          endDate: formattedDate,
        });

        if (selectedOutletId) {
          params.set('outletId', selectedOutletId);
        }

        const apiUrl = `/api/reports?${params.toString()}`;
        const response = await fetch(apiUrl);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Gagal memuat data untuk tanggal ${formattedDate}: ${response.statusText}`);
        }

        const data = await response.json();

        // Gabungkan data jam sibuk
        if (data.reports.peakHoursBookings && data.reports.peakHoursBookings.length > 0) {
          data.reports.peakHoursBookings.forEach((item: PeakHourData) => {
            const existingIndex = allPeakHoursBookings.findIndex(p => p.hour === item.hour);
            if (existingIndex >= 0) {
              allPeakHoursBookings[existingIndex].count += item.count;
            } else {
              allPeakHoursBookings.push({ ...item });
            }
          });
        }

        if (data.reports.peakHoursTransactions && data.reports.peakHoursTransactions.length > 0) {
          data.reports.peakHoursTransactions.forEach((item: PeakHourData) => {
            const existingIndex = allPeakHoursTransactions.findIndex(p => p.hour === item.hour);
            if (existingIndex >= 0) {
              allPeakHoursTransactions[existingIndex].count += item.count;
            } else {
              allPeakHoursTransactions.push({ ...item });
            }
          });
        }
      }

      // Urutkan data jam sibuk
      allPeakHoursBookings.sort((a, b) => b.count - a.count);
      allPeakHoursTransactions.sort((a, b) => b.count - a.count);

      // Simpan data jam sibuk berdasarkan hari dalam state terpisah
      setDayFilteredPeakHours({
        peakHoursBookings: allPeakHoursBookings,
        peakHoursTransactions: allPeakHoursTransactions,
      });

    } catch (err) {
      console.error('Error fetching day filtered data:', err);
      setDayFilteredPeakHours(null);
    } finally {
      setIsDayFilterLoading(false);
    }
  };

  // Fungsi untuk mengambil data perbandingan
  const fetchComparisonData = async () => {
    if (!startDate || !endDate || comparisonType === 'none') {
      setComparisonData(null);
      return;
    }

    setIsComparisonLoading(true);

    // Hitung tanggal perbandingan berdasarkan tipe perbandingan
    let comparisonStartDate: Date;
    let comparisonEndDate: Date;

    if (comparisonType === 'weekly') {
      // Minggu sebelumnya: kurangi 7 hari dari tanggal mulai dan akhir
      comparisonStartDate = new Date(startDate.getTime());
      comparisonStartDate.setDate(comparisonStartDate.getDate() - 7);

      comparisonEndDate = new Date(endDate.getTime());
      comparisonEndDate.setDate(comparisonEndDate.getDate() - 7);
    } else if (comparisonType === 'monthly') {
      // Bulan sebelumnya: kurangi 1 bulan dari tanggal mulai dan akhir
      comparisonStartDate = new Date(startDate.getTime());
      comparisonStartDate.setMonth(comparisonStartDate.getMonth() - 1);

      comparisonEndDate = new Date(endDate.getTime());
      comparisonEndDate.setMonth(comparisonEndDate.getMonth() - 1);
    } else {
      // Tipe perbandingan tidak valid
      setIsComparisonLoading(false);
      setComparisonData(null);
      return;
    }

    const formattedComparisonStartDate = formatLocalDate(comparisonStartDate);
    const formattedComparisonEndDate = formatLocalDate(comparisonEndDate);

    const params = new URLSearchParams({
      startDate: formattedComparisonStartDate,
      endDate: formattedComparisonEndDate,
    });

    if (selectedOutletId) {
      params.set('outletId', selectedOutletId);
    }

    const apiUrl = `/api/reports?${params.toString()}`;

    try {
      const response = await fetch(apiUrl);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Gagal memuat data perbandingan: ${response.statusText}`);
      }

      const data = await response.json();
      setComparisonData({
        peakHoursBookings: data.reports.peakHoursBookings || [],
        peakHoursTransactions: data.reports.peakHoursTransactions || [],
      });
    } catch (err) {
      console.error('Error fetching comparison data:', err);
      setComparisonData(null);
    } finally {
      setIsComparisonLoading(false);
    }
  };

  // Automatically fetch data when filters change
  useEffect(() => {
    if (selectedDay === null) {
      fetchReportData();
      fetchDailyRevenueData();
    }
  }, [startDate, endDate, selectedOutletId, selectedDay]);

  // Fetch day filtered data when day filter changes
  useEffect(() => {
    if (selectedDay !== null) {
      fetchDayFilteredData();
    }
  }, [selectedDay, startDate, endDate, selectedOutletId]);

  // Fetch comparison data when comparison type changes
  useEffect(() => {
    fetchComparisonData();
  }, [comparisonType, startDate, endDate, selectedOutletId]);

  // Efek untuk memproses data laporan
  useEffect(() => {
    // Proses data laporan jika diperlukan
  }, [reportData]);

  // --- Download Handlers ---
  const handleDownloadExcel = () => {
    if (!reportData) {
      alert('Tidak ada data laporan untuk diunduh.');
      return;
    }
    if (!startDate || !endDate) {
      alert('Silakan pilih rentang tanggal terlebih dahulu.');
      return;
    }

    try {
      const wb = XLSX.utils.book_new();
      const fileName = `Laporan_${selectedOutletId ? outlets.find(o=>o.id === selectedOutletId)?.name?.replace(/\s+/g, '_') || selectedOutletId : 'SemuaOutlet'}_${startDate.toISOString().split('T')[0]}_sd_${endDate.toISOString().split('T')[0]}.xlsx`;

      // 1. Ringkasan Umum
      const summaryData = [
        {"Metrik": "Total Pendapatan", "Nilai": formatCurrency(reportData.totalRevenue)},
        {"Metrik": "Tanggal Mulai", "Nilai": startDate.toLocaleDateString('id-ID')},
        {"Metrik": "Tanggal Selesai", "Nilai": endDate.toLocaleDateString('id-ID')},
        {"Metrik": "Outlet", "Nilai": selectedOutletId ? outlets.find(o=>o.id === selectedOutletId)?.name || 'Tidak Ditemukan' : 'Semua Outlet'}
      ];
      const wsSummary = XLSX.utils.json_to_sheet(summaryData);
      XLSX.utils.book_append_sheet(wb, wsSummary, "Ringkasan");

      // 2a. Jam Sibuk Transaksi
      if (reportData.peakHoursTransactions && reportData.peakHoursTransactions.length > 0) {
        const peakHoursTransactionsExcel = reportData.peakHoursTransactions.map(p => ({ "Jam": `${p.hour}:00`, "Jumlah Transaksi": p.count }));
        const wsPeakHoursTransactions = XLSX.utils.json_to_sheet(peakHoursTransactionsExcel);
        XLSX.utils.book_append_sheet(wb, wsPeakHoursTransactions, "Jam Sibuk Transaksi");
      }

      // 2b. Jam Sibuk Booking
      if (reportData.peakHoursBookings && reportData.peakHoursBookings.length > 0) {
        const peakHoursBookingsExcel = reportData.peakHoursBookings.map(p => ({ "Jam": `${p.hour}:00`, "Jumlah Booking": p.count }));
        const wsPeakHoursBookings = XLSX.utils.json_to_sheet(peakHoursBookingsExcel);
        XLSX.utils.book_append_sheet(wb, wsPeakHoursBookings, "Jam Sibuk Booking");
      }

      // 3. Layanan Populer
      if (reportData.popularServices && reportData.popularServices.length > 0) {
        const popularServicesExcel = reportData.popularServices.map(s => ({ "Nama Layanan": s.serviceName, "Jumlah Penggunaan": s.count }));
        const wsPopularServices = XLSX.utils.json_to_sheet(popularServicesExcel);
        XLSX.utils.book_append_sheet(wb, wsPopularServices, "Layanan Populer");
      }

      // 4. Kinerja Terapis
      if (reportData.therapistPerformance && reportData.therapistPerformance.length > 0) {
        const therapistPerfExcel = reportData.therapistPerformance.map(t => ({
           "Nama Terapis": t.therapistName,
           "Total Penjualan (Rp)": t.totalSales,
           "Total Komisi (Rp)": t.totalCommission
        }));
        const wsTherapistPerf = XLSX.utils.json_to_sheet(therapistPerfExcel);
        XLSX.utils.book_append_sheet(wb, wsTherapistPerf, "Kinerja Terapis");
      }

      // 4b. Kinerja Kasir
      if (reportData.cashierPerformance && reportData.cashierPerformance.length > 0) {
        const cashierPerfExcel = reportData.cashierPerformance.map(c => ({
           "Nama Kasir": c.cashierName,
           "Outlet": c.outletName,
           "Jumlah Transaksi": c.totalTransactions,
           "Total Pendapatan (Rp)": c.totalAmount
        }));
        const wsCashierPerf = XLSX.utils.json_to_sheet(cashierPerfExcel);
        XLSX.utils.book_append_sheet(wb, wsCashierPerf, "Kinerja Kasir");
      }

      // 5. Pelanggan Setia
      if (reportData.frequentCustomers && reportData.frequentCustomers.length > 0) {
        const frequentCustExcel = reportData.frequentCustomers.map(c => ({
           "Nama Pelanggan": c.customerName,
           "Telepon": c.customerPhone,
           "Jumlah Kunjungan": c.visitCount,
           "Total Pengeluaran (Rp)": c.totalSpent
        }));
        const wsFrequentCust = XLSX.utils.json_to_sheet(frequentCustExcel);
        XLSX.utils.book_append_sheet(wb, wsFrequentCust, "Pelanggan Setia");
      }

      // 6. Perbandingan Outlet (jika tidak filter per outlet)
      if (!selectedOutletId && reportData.outletSalesComparison && reportData.outletSalesComparison.length > 0) {
        const outletSalesExcel = reportData.outletSalesComparison.map(o => ({
           "Nama Outlet": o.outletName,
           "Total Penjualan (Rp)": o.totalSales
        }));
        const wsOutletSales = XLSX.utils.json_to_sheet(outletSalesExcel);
        XLSX.utils.book_append_sheet(wb, wsOutletSales, "Penjualan Outlet");
      }

      // Download file
      XLSX.writeFile(wb, fileName);

    } catch (error) {
      alert("Gagal membuat file Excel. Silakan coba lagi.");
    }
  };

  const handleDownloadPDF = async () => {
    if (!reportData) {
      alert('Tidak ada data laporan untuk diunduh.');
      return;
    }
     if (!startDate || !endDate) {
      alert('Silakan pilih rentang tanggal terlebih dahulu.');
      return;
    }

    try {
      const doc = new jsPDF();
      const pageHeight = doc.internal.pageSize.height || doc.internal.pageSize.getHeight();
      const pageWidth = doc.internal.pageSize.width || doc.internal.pageSize.getWidth();
      let startY = 15; // Posisi Y awal
      const margin = 15;
      const fileName = `Laporan_${selectedOutletId ? outlets.find(o=>o.id === selectedOutletId)?.name?.replace(/\s+/g, '_') || selectedOutletId : 'SemuaOutlet'}_${startDate.toISOString().split('T')[0]}_sd_${endDate.toISOString().split('T')[0]}.pdf`;

      // Judul
      doc.setFontSize(18);
      doc.text("Laporan Pendapatan", pageWidth / 2, startY, { align: 'center' });
      startY += 8;

      // Sub Judul (Outlet & Tanggal)
      const outletName = selectedOutletId ? outlets.find(o => o.id === selectedOutletId)?.name || 'Tidak Ditemukan' : 'Semua Outlet';
      const dateRange = `${startDate.toLocaleDateString('id-ID')} s/d ${endDate.toLocaleDateString('id-ID')}`;
      doc.setFontSize(12);
      doc.text(`Outlet: ${outletName}`, pageWidth / 2, startY, { align: 'center' });
      startY += 6;
      doc.text(`Periode: ${dateRange}`, pageWidth / 2, startY, { align: 'center' });
      startY += 10;

      // Fungsi helper untuk menambah tabel dan mengupdate Y
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const addTable = (title: string, head: string[][], body: any[][]) => {
          if (startY > pageHeight - 30) { // Cek jika butuh halaman baru
              doc.addPage();
              startY = 15;
          }
          doc.setFontSize(14);
          doc.text(title, margin, startY);
          startY += 8;
          autoTable(doc, {
              head: head,
              body: body,
              startY: startY,
              margin: { left: margin, right: margin },
              theme: 'grid',
              headStyles: { fillColor: [22, 160, 133] }, // Warna Teal
              didDrawPage: (data) => {
                  startY = data.cursor?.y ? data.cursor.y + 5 : startY; // Update Y setelah tabel digambar
              }
          });
           // Pastikan Y diperbarui bahkan jika tabel kosong atau tidak digambar
          // @ts-expect-error: Akses lastAutoTable.finalY (tipe internal jspdf-autotable)
          if (!(doc as any).lastAutoTable.finalY) {
               startY += 5; // Beri sedikit spasi jika tabel kosong
          } else {
              // @ts-expect-error: Akses lastAutoTable.finalY (tipe internal jspdf-autotable)
              startY = (doc as any).lastAutoTable.finalY + 5;
          }
      };

      // Fungsi helper untuk menambahkan chart ke PDF
      const addChartToPdf = async (chartRef: React.RefObject<HTMLDivElement>, title: string) => {
        if (chartRef.current) {
          if (startY > pageHeight - 30) { // Cek jika butuh halaman baru
            doc.addPage();
            startY = 15;
          }

          // Tambahkan judul chart
          doc.setFontSize(14);
          doc.text(title, margin, startY);
          startY += 8;

          try {
            // Konversi chart ke gambar PNG
            const imgData = await htmlToImage.toPng(chartRef.current);

            // Dapatkan dimensi chart
            const chartWidth = chartRef.current.offsetWidth;
            const chartHeight = chartRef.current.offsetHeight;

            // Hitung rasio untuk memastikan chart muat di halaman PDF
            const availableWidth = pageWidth - (margin * 2);
            let imgWidth = availableWidth;
            let imgHeight = (chartHeight * availableWidth) / chartWidth;

            // Jika tinggi gambar terlalu besar, tambahkan halaman baru
            if (startY + imgHeight > pageHeight - margin) {
              doc.addPage();
              startY = 15;
            }

            // Tambahkan gambar ke PDF
            doc.addImage(imgData, 'PNG', margin, startY, imgWidth, imgHeight);
            startY += imgHeight + 10; // Tambahkan spasi setelah chart
          } catch (error) {
            console.error('Error converting chart to image:', error);
          }
        }
      };

      // Tambahkan chart pendapatan harian jika ada
      if (dailyRevenueChartRef.current && dailyRevenueData.length > 0) {
        await addChartToPdf(dailyRevenueChartRef, "Grafik Pendapatan Harian");
      }

      // Tambahkan chart jam sibuk jika ada dan bukan investor
      if (!isInvestor && peakHoursChartRef.current) {
        await addChartToPdf(peakHoursChartRef, "Grafik Analisis Jam Sibuk");
      }

      // Tambahkan chart perbandingan outlet jika ada
      if (outletSalesChartRef.current && (!selectedOutletId || outletSalesChartData.length > 1)) {
        await addChartToPdf(outletSalesChartRef, "Grafik Perbandingan Penjualan Outlet");
      }

      // Tambahkan chart layanan populer jika ada dan bukan investor
      if (!isInvestor && popularServicesChartRef.current && reportData.popularServices.length > 0) {
        await addChartToPdf(popularServicesChartRef, "Grafik Layanan Terpopuler");
      }

      // Tambahkan chart kinerja terapis jika ada dan bukan investor
      if (!isInvestor && therapistPerformanceChartRef.current && reportData.therapistPerformance.length > 0) {
        await addChartToPdf(therapistPerformanceChartRef, "Grafik Kinerja Terapis");
      }

      // Tambahkan tabel kinerja kasir jika ada
      if (reportData.cashierPerformance && reportData.cashierPerformance.length > 0) {
        const cashierPerfHead = [['Nama Kasir', 'Outlet', 'Jumlah Transaksi', 'Total Pendapatan']];
        const cashierPerfBody = reportData.cashierPerformance.map(c => [
          c.cashierName,
          c.outletName,
          c.totalTransactions,
          formatCurrency(c.totalAmount)
        ]);
        addTable("Kinerja Kasir", cashierPerfHead, cashierPerfBody);
      }

      // Tabel Jam Sibuk Transaksi
      if (reportData.peakHoursTransactions && reportData.peakHoursTransactions.length > 0) {
        const peakHoursTransactionsHead = [['Jam', 'Jumlah Transaksi']];
        const peakHoursTransactionsBody = reportData.peakHoursTransactions.map(p => [`${p.hour}:00`, p.count]);
        addTable("Analisis Jam Sibuk Transaksi", peakHoursTransactionsHead, peakHoursTransactionsBody);
      }

      // Tabel Jam Sibuk Booking
      if (reportData.peakHoursBookings && reportData.peakHoursBookings.length > 0) {
        const peakHoursBookingsHead = [['Jam', 'Jumlah Booking']];
        const peakHoursBookingsBody = reportData.peakHoursBookings.map(p => [`${p.hour}:00`, p.count]);
        addTable("Analisis Jam Sibuk Booking", peakHoursBookingsHead, peakHoursBookingsBody);
      }

      // Tabel Layanan Populer
      if (reportData.popularServices && reportData.popularServices.length > 0) {
        const popularServicesHead = [['Nama Layanan', 'Jumlah Penggunaan']];
        const popularServicesBody = reportData.popularServices.map(s => [s.serviceName, s.count]);
        addTable("Layanan Terpopuler", popularServicesHead, popularServicesBody);
      }

      // Tabel Kinerja Terapis
      if (reportData.therapistPerformance && reportData.therapistPerformance.length > 0) {
        const therapistPerfHead = [['Nama Terapis', 'Total Penjualan', 'Total Komisi']];
        const therapistPerfBody = reportData.therapistPerformance.map(t => [
          t.therapistName,
          formatCurrency(t.totalSales),
          formatCurrency(t.totalCommission)
        ]);
        addTable("Kinerja Terapis", therapistPerfHead, therapistPerfBody);
      }

      // Tabel Pelanggan Setia
      if (reportData.frequentCustomers && reportData.frequentCustomers.length > 0) {
        const frequentCustHead = [['Nama Pelanggan', 'Telepon', 'Jml Kunjungan', 'Total Pengeluaran']];
        const frequentCustBody = reportData.frequentCustomers.map(c => [
          c.customerName,
          c.customerPhone,
          c.visitCount,
          formatCurrency(c.totalSpent)
        ]);
        addTable("Pelanggan Paling Setia", frequentCustHead, frequentCustBody);
      }

      // Tabel Perbandingan Outlet (jika tidak filter per outlet)
      if (!selectedOutletId && reportData.outletSalesComparison && reportData.outletSalesComparison.length > 0) {
        const outletSalesHead = [['Nama Outlet', 'Total Penjualan']];
        const outletSalesBody = reportData.outletSalesComparison.map(o => [
          o.outletName,
          formatCurrency(o.totalSales)
        ]);
        addTable("Perbandingan Penjualan per Outlet", outletSalesHead, outletSalesBody);
      }

      // Download file
      doc.save(fileName);

    } catch (error) {
       console.error('Error generating PDF:', error);
       alert("Gagal membuat file PDF. Silakan coba lagi.");
    }
  };



  // Data untuk Pie Chart Layanan Teramai (BARU)
  const popularServicesPieData = useMemo(() => {
    if (!reportData?.popularServices) return [];
    return reportData.popularServices.map(service => ({
       name: service.serviceName, // Pie chart butuh key 'name'
       value: service.count        // Pie chart butuh key 'value'
    }));
  }, [reportData?.popularServices]);

  // Data untuk Grouped Bar Chart Kinerja Terapis (BARU)
  const therapistPerformanceChartData = useMemo(() => {
     if (!reportData?.therapistPerformance) return [];
     // Ambil top 10 terapis berdasarkan sales untuk chart
     return reportData.therapistPerformance.slice(0, 10).map(therapist => ({
        name: therapist.therapistName,
        Penjualan: therapist.totalSales,
        Komisi: therapist.totalCommission
     }));
  }, [reportData?.therapistPerformance]);

  // Data Olahan untuk Chart Perbandingan Outlet (BARU)
  const outletSalesChartData = useMemo(() => {
     if (!reportData?.outletSalesComparison) return [];
     return reportData.outletSalesComparison.map(outlet => ({
        name: outlet.outletName,
        Penjualan: outlet.totalSales
     }));
  }, [reportData?.outletSalesComparison]);

  // Data untuk Line Chart Pendapatan Harian
  const dailyRevenueChartData = useMemo(() => {
    return dailyRevenueData.map(item => ({
      date: item.date,
      Pendapatan: item.revenue,
      // Format tanggal untuk tampilan tooltip
      formattedDate: new Date(item.date).toLocaleDateString('id-ID', { day: 'numeric', month: 'short', year: 'numeric' })
    }));
  }, [dailyRevenueData]);

  return (
    <motion.div
      className="container mx-auto p-4 md:p-6 space-y-6"
      initial="hidden"
      animate="visible"
      variants={staggerContainer}
    >
      {/* Header */}
      <motion.div variants={fadeInUp}>
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Laporan</h1>
        <p className="text-gray-600">Analisis data transaksi dan kinerja.</p>
      </motion.div>

      {/* Menu Navigasi ke Sub-halaman Reports */}
      <motion.div variants={fadeInUp} className="flex flex-wrap gap-2 mb-4">
        <Link href="/dashboard/reports/analytics" className="btn btn-sm btn-outline gap-1">
          <FiBarChart2 className="h-4 w-4" /> Analitik Lanjutan
        </Link>
        <Link href="/dashboard/reports/service-performance" className="btn btn-sm btn-outline gap-1">
          <FiTrendingUp className="h-4 w-4" /> Performa Layanan
        </Link>
        <Link href="/dashboard/reports/customer-segmentation" className="btn btn-sm btn-outline gap-1">
          <FiUsers className="h-4 w-4" /> Segmentasi Pelanggan
        </Link>
        <Link href="/dashboard/crm" className="btn btn-sm btn-outline gap-1 bg-primary/10 text-primary">
          <FiUsers className="h-4 w-4" /> CRM
        </Link>
        <Link href="/dashboard/reports/booking-conversion" className="btn btn-sm btn-outline gap-1">
          <FiCalendar className="h-4 w-4" /> Konversi Booking
        </Link>
        <Link href="/dashboard/reports/visit-patterns" className="btn btn-sm btn-outline gap-1">
          <FiClock className="h-4 w-4" /> Pola Kunjungan
        </Link>
        <Link href="/dashboard/reports/captain-performance" className="btn btn-sm btn-outline gap-1">
          <FiUserCheck className="h-4 w-4" /> Kinerja Kapten
        </Link>
      </motion.div>

      {/* Filters Section */}
      <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Tanggal Mulai */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiCalendar className="text-gray-600" /> Tanggal Mulai
            </label>
            <div className="dropdown dropdown-bottom w-full">
              <div tabIndex={0} role="button" className="btn btn-sm btn-outline w-full justify-between overflow-hidden">
                <span className="truncate">{startDate ? formatDateDisplay(startDate) : 'Pilih tanggal mulai'}</span>
                <span className="text-xs">▼</span>
              </div>
              <div tabIndex={0} className="dropdown-content z-[1] card card-compact shadow bg-base-100 rounded-box w-64">
                <div className="card-body p-2">
                  <div className="w-full max-w-[300px] mx-auto">
                    <div className="flex justify-between items-center py-2 mb-2">
                      <button className="btn btn-sm btn-ghost" onClick={() => setStartDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() - 1, 1) : null)}>
                        «
                      </button>
                      <div className="text-sm font-medium">
                        {startDate ? format(startDate, 'MMMM yyyy', { locale: idLocale }) : 'Pilih tanggal'}
                      </div>
                      <button className="btn btn-sm btn-ghost" onClick={() => setStartDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() + 1, 1) : null)}>
                        »
                      </button>
                    </div>
                    <div className="grid grid-cols-7 gap-1">
                      {/* Hari dalam seminggu */}
                      {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                        <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                      ))}

                      {/* Tanggal */}
                      {startDate && Array.from({ length: 42 }, (_, i) => {
                        const firstDayOfMonth = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
                        const startingDayOfWeek = firstDayOfMonth.getDay();
                        const day = i - startingDayOfWeek + 1;
                        const date = new Date(startDate.getFullYear(), startDate.getMonth(), day);
                        const isCurrentMonth = date.getMonth() === startDate.getMonth();
                        const isSelected = startDate && date.getDate() === startDate.getDate() &&
                                          date.getMonth() === startDate.getMonth() &&
                                          date.getFullYear() === startDate.getFullYear();
                        const isToday = new Date().toDateString() === date.toDateString();

                        return (
                          <button
                            key={i}
                            className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all ${isCurrentMonth ? '' : 'text-gray-300'}
                                      ${isSelected ? 'bg-primary text-primary-content' : ''}
                                      ${isToday && !isSelected ? 'border border-primary text-primary' : ''}
                                      ${isCurrentMonth && !isSelected ? 'hover:bg-base-200' : ''}`}
                            onClick={() => setStartDate(new Date(date))}
                            disabled={!isCurrentMonth}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Tanggal Akhir */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiCalendar className="text-gray-600" /> Tanggal Akhir
            </label>
            <div className="dropdown dropdown-bottom w-full">
              <div tabIndex={0} role="button" className="btn btn-sm btn-outline w-full justify-between overflow-hidden">
                <span className="truncate">{endDate ? formatDateDisplay(endDate) : 'Pilih tanggal akhir'}</span>
                <span className="text-xs">▼</span>
              </div>
              <div tabIndex={0} className="dropdown-content z-[1] card card-compact shadow bg-base-100 rounded-box w-64">
                <div className="card-body p-2">
                  <div className="w-full max-w-[300px] mx-auto">
                    <div className="flex justify-between items-center py-2 mb-2">
                      <button className="btn btn-sm btn-ghost" onClick={() => setEndDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() - 1, 1) : null)}>
                        «
                      </button>
                      <div className="text-sm font-medium">
                        {endDate ? format(endDate, 'MMMM yyyy', { locale: idLocale }) : 'Pilih tanggal'}
                      </div>
                      <button className="btn btn-sm btn-ghost" onClick={() => setEndDate(prev => prev ? new Date(prev.getFullYear(), prev.getMonth() + 1, 1) : null)}>
                        »
                      </button>
                    </div>
                    <div className="grid grid-cols-7 gap-1">
                      {/* Hari dalam seminggu */}
                      {['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'].map(day => (
                        <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">{day}</div>
                      ))}

                      {/* Tanggal */}
                      {endDate && Array.from({ length: 42 }, (_, i) => {
                        const firstDayOfMonth = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
                        const startingDayOfWeek = firstDayOfMonth.getDay();
                        const day = i - startingDayOfWeek + 1;
                        const date = new Date(endDate.getFullYear(), endDate.getMonth(), day);
                        const isCurrentMonth = date.getMonth() === endDate.getMonth();
                        const isSelected = endDate && date.getDate() === endDate.getDate() &&
                                          date.getMonth() === endDate.getMonth() &&
                                          date.getFullYear() === endDate.getFullYear();
                        const isToday = new Date().toDateString() === date.toDateString();
                        const isDisabled = !isCurrentMonth || (startDate && date < startDate) || date > new Date();

                        return (
                          <button
                            key={i}
                            className={`flex items-center justify-center text-center p-1 text-sm rounded-full w-8 h-8 transition-all ${isCurrentMonth ? '' : 'text-gray-300'}
                                      ${isSelected ? 'bg-primary text-primary-content' : ''}
                                      ${isToday && !isSelected ? 'border border-primary text-primary' : ''}
                                      ${isCurrentMonth && !isSelected && !isDisabled ? 'hover:bg-base-200' : ''}
                                      ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                            onClick={() => !isDisabled && setEndDate(new Date(date))}
                            disabled={isDisabled}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Filter Outlet */}
          <div className="flex flex-col gap-1">
            <label className="text-xs text-gray-500 flex items-center gap-1 mb-1">
              <FiFilter className="text-gray-600" /> Outlet
            </label>
            {isInvestor ? (
              <div className="flex items-center gap-2">
                {allowedOutlets.length === 1 ? (
                  <div className="flex items-center gap-1 text-amber-600 text-sm">
                    <FiLock className="text-amber-600" />
                    <span className="truncate">
                      {allowedOutlets[0]?.name || 'Outlet'}
                    </span>
                  </div>
                ) : (
                  <select
                    className="select select-bordered select-sm w-full border-amber-500"
                    value={selectedOutletId}
                    onChange={(e) => setSelectedOutletId(e.target.value)}
                  >
                    {allowedOutlets.map(outlet => (
                      <option key={outlet.id} value={outlet.id}>{outlet.name}</option>
                    ))}
                  </select>
                )}
              </div>
            ) : (
              <select
                className="select select-bordered select-sm w-full"
                value={selectedOutletId}
                onChange={(e) => setSelectedOutletId(e.target.value)}
              >
                <option value="">Semua Outlet</option>
                {outlets.map(outlet => (
                  <option key={outlet.id} value={outlet.id}>{outlet.name}</option>
                ))}
              </select>
            )}
          </div>

          {/* Tombol Download */}
          <div className="flex flex-col gap-1 justify-end">
            <label className="text-xs text-gray-500 mb-1">Unduh Laporan</label>
            <div className="flex gap-2">
              <button
                onClick={handleDownloadExcel}
                className="btn btn-sm btn-outline btn-primary flex-1 gap-1"
                disabled={isLoading || !reportData}
                title="Unduh Laporan (Excel)"
              >
                <FiGrid className="hidden sm:inline-block" /> Excel
              </button>
              <button
                onClick={handleDownloadPDF}
                className="btn btn-sm btn-outline btn-secondary flex-1 gap-1"
                disabled={isLoading || !reportData}
                title="Unduh Laporan (PDF)"
              >
                <FiFileText className="hidden sm:inline-block" /> PDF
              </button>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Loading State */}
      {isLoading && (
         <motion.div variants={fadeInUp} className="text-center py-10">
            <span className="loading loading-spinner loading-lg text-primary"></span>
            <p className="mt-2 text-gray-600">Memuat data laporan...</p>
         </motion.div>
      )}

      {/* Error State */}
      {error && !isLoading && (
        <motion.div variants={fadeInUp} className="alert alert-error">
          <FiAlertTriangle />
          <span>Error: {error}</span>
        </motion.div>
      )}

      {/* Report Data Display */}
      {!isLoading && !error && reportData && (
        <>
          {/* Ringkasan Total Revenue (BARU) */}
          <motion.div variants={fadeInUp} className="bg-gradient-to-r from-primary to-secondary text-white p-4 sm:p-6 rounded-lg shadow-lg">
             <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
                <div>
                   <h2 className="text-lg font-semibold mb-1">Total Penjualan</h2>
                   <p className="text-xs opacity-80 break-words">
                     ({startDate?.toLocaleDateString('id-ID', { day: 'numeric', month:'short', year:'numeric'})} - {endDate?.toLocaleDateString('id-ID', { day: 'numeric', month:'short', year:'numeric'})}
                     <span className="block sm:inline">{selectedOutletId ? ` - ${outlets.find(o=>o.id === selectedOutletId)?.name}`: ' - Semua Outlet'}</span>)
                   </p>
                </div>
                <p className="text-2xl sm:text-3xl font-bold">{formatCurrency(reportData.totalRevenue)}</p>
             </div>
          </motion.div>

          {/* Daily Revenue Chart */}
          <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow col-span-1 lg:col-span-2 mb-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-700">
              <FiBarChart2 /> Pendapatan Harian
            </h2>
            {isDailyRevenueLoading ? (
              <div className="flex justify-center items-center h-64">
                <span className="loading loading-spinner loading-lg text-primary"></span>
              </div>
            ) : dailyRevenueError ? (
              <div className="alert alert-error">
                <FiAlertTriangle />
                <span>Error: {dailyRevenueError}</span>
              </div>
            ) : dailyRevenueData.length === 0 ? (
              <div className="text-center py-10 bg-white rounded-lg">
                <FiBarChart2 className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-lg font-medium text-gray-900">Data Pendapatan Harian Kosong</h3>
                <p className="mt-1 text-sm text-gray-500">Tidak ada data pendapatan harian untuk periode yang dipilih.</p>
              </div>
            ) : (
              <div className="w-full overflow-x-auto pb-2">
                <div ref={dailyRevenueChartRef} style={{ width: '100%', minWidth: '500px', height: '300px' }}>
                  <ResponsiveContainer>
                    <AreaChart data={dailyRevenueChartData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                      <defs>
                        <linearGradient id="colorPendapatan" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
                      <XAxis
                        dataKey="date"
                        tick={{ fill: '#6b7280' }}
                        tickFormatter={(value) => {
                          const date = new Date(value);
                          return date.getDate().toString();
                        }}
                      />
                      <YAxis
                        tick={{ fill: '#6b7280' }}
                        tickFormatter={(value) => new Intl.NumberFormat('id-ID', { notation: 'compact', compactDisplay: 'short' }).format(value)}
                      />
                      <Tooltip
                        formatter={(value: number) => formatCurrency(value)}
                        labelFormatter={(value) => {
                          const item = dailyRevenueChartData.find(item => item.date === value);
                          return item ? item.formattedDate : value;
                        }}
                        cursor={{ fill: '#f3f4f6' }}
                      />
                      <Area
                        type="monotone"
                        dataKey="Pendapatan"
                        stroke="#10b981"
                        fillOpacity={1}
                        fill="url(#colorPendapatan)"
                        activeDot={{ r: 6 }}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </div>
            )}
          </motion.div>

          {/* Pesan khusus untuk investor */}
          {isInvestor && (
            <motion.div variants={fadeInUp} className="bg-amber-50 border border-amber-200 p-4 rounded-lg shadow mb-6">
              <div className="flex items-start gap-3">
                <FiLock className="text-amber-600 text-xl mt-1" />
                <div>
                  <h3 className="font-medium text-amber-800">Akses Terbatas</h3>
                  <p className="text-amber-700 text-sm mt-1">
                    Sebagai investor, Anda memiliki akses terbatas ke data laporan. Anda hanya dapat melihat informasi pendapatan dan perbandingan outlet.
                  </p>
                </div>
              </div>
            </motion.div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
             {/* Peak Hours Analysis - Hanya tampilkan jika bukan investor */}
             {!isInvestor && (
               <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow col-span-1 lg:col-span-2">
                  <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
                    <h2 className="text-lg font-semibold flex items-center gap-2 text-gray-700"><FiClock /> Analisis Jam Sibuk</h2>
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 mt-2 sm:mt-0">
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-gray-500">Hari:</span>
                        <select
                          className="select select-bordered select-xs"
                          value={selectedDay === null ? 'all' : selectedDay.toString()}
                          onChange={(e) => {
                            const value = e.target.value;
                            setSelectedDay(value === 'all' ? null : parseInt(value, 10));
                          }}
                          aria-label="Pilih hari"
                        >
                          <option value="all">Semua Hari</option>
                          {dayNames.map((day, index) => (
                            <option key={index} value={index.toString()}>{day}</option>
                          ))}
                        </select>
                        {isDayFilterLoading && (
                          <span className="loading loading-spinner loading-xs text-primary"></span>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-gray-500">Bandingkan dengan:</span>
                        <select
                          className="select select-bordered select-xs"
                          value={comparisonType}
                          onChange={(e) => setComparisonType(e.target.value)}
                          aria-label="Pilih perbandingan"
                        >
                          <option value="none">Tidak ada</option>
                          <option value="weekly">Minggu sebelumnya</option>
                          <option value="monthly">Bulan sebelumnya</option>
                        </select>
                        {isComparisonLoading && (
                          <span className="loading loading-spinner loading-xs text-primary"></span>
                        )}
                      </div>
                    </div>
                  </div>
                  {selectedDay !== null && (
                    <div className="text-sm text-center mb-2 text-gray-600">
                      Menampilkan data jam sibuk untuk hari <span className="font-semibold">{dayNames[selectedDay]}</span>
                      {isDayFilterLoading && (
                        <span className="loading loading-spinner loading-xs ml-2 text-primary"></span>
                      )}
                    </div>
                  )}
                  <div className="w-full overflow-x-auto pb-2">
                     <div ref={peakHoursChartRef} style={{ width: '100%', minWidth: '500px', height: '300px' }}>
                        <ResponsiveContainer>
                           {/* Persiapkan data untuk chart */}
                           {(() => {
                              // Buat array untuk semua jam operasional (9-23)
                              const hours = Array.from({length: 15}, (_, i) => i + 9);

                              // Buat map untuk data booking
                              const bookingMap = new Map();

                              // Gunakan data yang difilter berdasarkan hari jika ada, jika tidak gunakan data normal
                              const peakHoursBookingsData = selectedDay !== null && dayFilteredPeakHours
                                ? dayFilteredPeakHours.peakHoursBookings
                                : (reportData.peakHoursBookings || []);

                              if (peakHoursBookingsData.length > 0) {
                                 peakHoursBookingsData.forEach(item => {
                                    // Pastikan jam dalam rentang 0-23
                                    const hour = parseInt(String(item.hour), 10);
                                    if (!isNaN(hour) && hour >= 0 && hour < 24) {
                                       bookingMap.set(hour, item.count);
                                    }
                                 });
                              }

                              // Buat map untuk data transaksi
                              const transactionMap = new Map();

                              // Gunakan data yang difilter berdasarkan hari jika ada, jika tidak gunakan data normal
                              const peakHoursTransactionsData = selectedDay !== null && dayFilteredPeakHours
                                ? dayFilteredPeakHours.peakHoursTransactions
                                : (reportData.peakHoursTransactions || []);

                              if (peakHoursTransactionsData.length > 0) {
                                 peakHoursTransactionsData.forEach(item => {
                                    // Pastikan jam dalam rentang 0-23
                                    const hour = parseInt(String(item.hour), 10);
                                    if (!isNaN(hour) && hour >= 0 && hour < 24) {
                                       // Hanya tampilkan jam operasional (9-23)
                                       if (hour >= 9 && hour <= 23) {
                                          transactionMap.set(hour, item.count);
                                       }
                                    }
                                 });
                              }

                              // Buat map untuk data perbandingan jika ada
                              const comparisonBookingMap = new Map();
                              const comparisonTransactionMap = new Map();

                              if (comparisonData && comparisonType !== 'none') {
                                 // Data booking perbandingan
                                 if (comparisonData.peakHoursBookings && comparisonData.peakHoursBookings.length > 0) {
                                    comparisonData.peakHoursBookings.forEach(item => {
                                       const hour = parseInt(String(item.hour), 10);
                                       if (!isNaN(hour) && hour >= 9 && hour <= 23) {
                                          comparisonBookingMap.set(hour, item.count);
                                       }
                                    });
                                 }

                                 // Data transaksi perbandingan
                                 if (comparisonData.peakHoursTransactions && comparisonData.peakHoursTransactions.length > 0) {
                                    comparisonData.peakHoursTransactions.forEach(item => {
                                       const hour = parseInt(String(item.hour), 10);
                                       if (!isNaN(hour) && hour >= 9 && hour <= 23) {
                                          comparisonTransactionMap.set(hour, item.count);
                                       }
                                    });
                                 }
                              }

                              // Gabungkan data untuk semua jam
                              const combinedData = hours.map(hour => {
                                 const data: any = {
                                    hour,
                                    booking: bookingMap.get(hour) || 0,
                                    transaction: transactionMap.get(hour) || 0
                                 };

                                 // Tambahkan data perbandingan jika ada
                                 if (comparisonType !== 'none' && comparisonData) {
                                    data.comparisonBooking = comparisonBookingMap.get(hour) || 0;
                                    data.comparisonTransaction = comparisonTransactionMap.get(hour) || 0;
                                 }

                                 return data;
                              });

                              // Tentukan label perbandingan berdasarkan tipe
                              let comparisonLabel = '';
                              if (comparisonType === 'weekly') {
                                 comparisonLabel = 'Minggu Sebelumnya';
                              } else if (comparisonType === 'monthly') {
                                 comparisonLabel = 'Bulan Sebelumnya';
                              }

                              return (
                                 <LineChart data={combinedData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                                    <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0"/>
                                    <XAxis
                                       dataKey="hour"
                                       type="number"
                                       domain={[9, 23]}
                                       ticks={hours}
                                       tickFormatter={(hour) => `${hour}:00`}
                                       fontSize={10}
                                       tick={{ fill: '#6b7280' }}
                                    />
                                    <YAxis fontSize={10} tick={{ fill: '#6b7280' }} />
                                    <Tooltip
                                       formatter={(value: number, name: string) => {
                                          // Pastikan label sesuai dengan warna garis
                                          if (name === 'booking') {
                                             return [value, 'Booking'];
                                          } else if (name === 'transaction') {
                                             return [value, 'Transaksi'];
                                          } else if (name === 'comparisonBooking') {
                                             return [value, `Booking (${comparisonLabel})`];
                                          } else if (name === 'comparisonTransaction') {
                                             return [value, `Transaksi (${comparisonLabel})`];
                                          }
                                          return [value, name];
                                       }}
                                       labelFormatter={(hour) => `Jam ${hour}:00`}
                                       cursor={{ fill: '#f3f4f6' }}
                                       contentStyle={{ backgroundColor: 'white', border: '1px solid #e0e0e0' }}
                                    />
                                    <Legend
                                       wrapperStyle={{fontSize: "12px"}}
                                       formatter={(value) => {
                                          // Pastikan label sesuai dengan warna garis
                                          if (value === 'booking') {
                                             return <span style={{ color: '#f59e0b' }}>Booking</span>;
                                          } else if (value === 'transaction') {
                                             return <span style={{ color: '#10b981' }}>Transaksi</span>;
                                          } else if (value === 'comparisonBooking') {
                                             return <span style={{ color: '#f59e0b', opacity: 0.5 }}>Booking ({comparisonLabel})</span>;
                                          } else if (value === 'comparisonTransaction') {
                                             return <span style={{ color: '#10b981', opacity: 0.5 }}>Transaksi ({comparisonLabel})</span>;
                                          }
                                          return value;
                                       }}
                                    />

                                    {/* Garis perbandingan (shadow line) jika ada */}
                                    {comparisonType !== 'none' && comparisonData && (
                                      <>
                                        {/* Garis untuk Booking Perbandingan - warna oranye dengan opacity rendah */}
                                        <Line
                                          type="monotone"
                                          dataKey="comparisonBooking"
                                          stroke="#f59e0b"
                                          name="comparisonBooking"
                                          strokeWidth={2}
                                          strokeDasharray="5 5"
                                          strokeOpacity={0.5}
                                          dot={{ r: 2, strokeOpacity: 0.5, fillOpacity: 0.5 }}
                                          activeDot={{ r: 4, strokeOpacity: 0.7 }}
                                        />
                                        {/* Garis untuk Transaksi Perbandingan - warna hijau tosca dengan opacity rendah */}
                                        <Line
                                          type="monotone"
                                          dataKey="comparisonTransaction"
                                          stroke="#10b981"
                                          name="comparisonTransaction"
                                          strokeWidth={2}
                                          strokeDasharray="5 5"
                                          strokeOpacity={0.5}
                                          dot={{ r: 2, strokeOpacity: 0.5, fillOpacity: 0.5 }}
                                          activeDot={{ r: 4, strokeOpacity: 0.7 }}
                                        />
                                      </>
                                    )}

                                    {/* Garis untuk Booking - warna oranye */}
                                    <Line
                                       type="monotone"
                                       dataKey="booking"
                                       stroke="#f59e0b"
                                       name="booking"
                                       strokeWidth={2}
                                       dot={{ r: 3 }}
                                       activeDot={{ r: 5 }}
                                    />
                                    {/* Garis untuk Transaksi - warna hijau tosca */}
                                    <Line
                                       type="monotone"
                                       dataKey="transaction"
                                       stroke="#10b981"
                                       name="transaction"
                                       strokeWidth={2}
                                       dot={{ r: 3 }}
                                       activeDot={{ r: 5 }}
                                    />
                                 </LineChart>
                              );
                           })()}
                        </ResponsiveContainer>
                     </div>
                  </div>
                  <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-4">
                     <div className="bg-base-100 p-3 rounded-lg border border-gray-200">
                        <h3 className="text-sm font-medium mb-2 text-gray-700">Jam Sibuk Booking</h3>
                        <div className="flex flex-wrap gap-2">
                           {reportData.peakHoursBookings && reportData.peakHoursBookings.length > 0 ? (
                              reportData.peakHoursBookings
                                .filter(item => item.hour >= 9 && item.hour <= 23) // Filter hanya jam operasional
                                .map((item) => ( // Tampilkan semua jam, tanpa batasan 5 teratas
                                 <span key={`booking-${item.hour}`} className="badge gap-1" style={{ backgroundColor: '#f59e0b', color: 'white' }}>
                                    {item.hour}:00 <span className="badge badge-sm">{item.count}</span>
                                 </span>
                              ))
                           ) : (
                              <p className="text-sm text-gray-500">Tidak ada data jam sibuk booking.</p>
                           )}
                        </div>
                     </div>
                     <div className="bg-base-100 p-3 rounded-lg border border-gray-200">
                        <h3 className="text-sm font-medium mb-2 text-gray-700">Jam Sibuk Transaksi</h3>
                        <div className="flex flex-wrap gap-2">
                           {reportData.peakHoursTransactions && reportData.peakHoursTransactions.length > 0 ? (
                              reportData.peakHoursTransactions
                                .filter(item => item.hour >= 9 && item.hour <= 23) // Filter hanya jam operasional
                                .map((item) => ( // Tampilkan semua jam, tanpa batasan 5 teratas
                                  <button
                                    key={`transaction-${item.hour}`}
                                    className="badge gap-1 cursor-pointer hover:opacity-80 transition-opacity"
                                    style={{ backgroundColor: '#10b981', color: 'white', border: 'none' }}
                                    onClick={() => handleHourClick(item.hour)}
                                    type="button"
                                    title={`Lihat detail transaksi jam ${item.hour}:00`}
                                  >
                                     {item.hour}:00 <span className="badge badge-sm">{item.count}</span>
                                  </button>
                              ))
                           ) : (
                              <p className="text-sm text-gray-500">Tidak ada data jam sibuk transaksi.</p>
                           )}
                        </div>
                     </div>
                  </div>
               </motion.div>
             )}

             {/* Outlet Sales Comparison Chart (BARU) - Hanya tampilkan jika tidak ada filter outlet atau ada lebih dari 1 outlet */}
             {(!selectedOutletId || outletSalesChartData.length > 1) && (
                <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow col-span-1 lg:col-span-2">
                   <h2 className="text-lg font-semibold mb-2 flex items-center gap-2 text-gray-700"><FiBarChart2 /> Perbandingan Penjualan Outlet</h2>
                   <p className="text-xs text-gray-500 mb-4">({startDate?.toLocaleDateString('id-ID', { day: 'numeric', month:'short', year:'numeric'})} - {endDate?.toLocaleDateString('id-ID', { day: 'numeric', month:'short', year:'numeric'})})</p>
                   <div className="w-full overflow-x-auto pb-2">
                      <div ref={outletSalesChartRef} style={{ width: '100%', minWidth: '500px', height: '300px' }}>
                         <ResponsiveContainer>
                            <BarChart data={outletSalesChartData} layout="vertical" margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                               <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0"/>
                               <XAxis type="number" fontSize={10} tickFormatter={(value) => new Intl.NumberFormat('id-ID', { notation: 'compact' }).format(value)} tick={{ fill: '#6b7280' }}/>
                               <YAxis type="category" dataKey="name" width={100} fontSize={10} tick={{ fill: '#6b7280' }} />
                               <Tooltip formatter={(value: number) => formatCurrency(value)} cursor={{ fill: '#f3f4f6' }}/>
                               <Bar dataKey="Penjualan" fill={LOGO_COLORS[3]} name="Total Penjualan" barSize={20}/>
                            </BarChart>
                         </ResponsiveContainer>
                      </div>
                   </div>
                </motion.div>
             )}

             {/* Popular Services - Hanya tampilkan jika bukan investor */}
             {!isInvestor && (
               <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow">
                  <h2 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-700"><FiTrendingUp /> Layanan Teramai (Top 10)</h2>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    {/* Table (Lebar Dibatasi) */}
                    <div className="overflow-x-auto max-h-72 w-full">
                       <table className="table table-sm w-full">
                          <thead><tr><th>Layanan</th><th className="text-right">Jml</th></tr></thead>
                          <tbody>
                             {reportData.popularServices.length > 0 ? (
                                reportData.popularServices.map((service, index) => (
                                   <tr key={service.serviceId}>
                                     <td className="flex items-center">
                                        <span className="inline-block w-3 h-3 rounded-full mr-2" style={{ backgroundColor: LOGO_COLORS[index % LOGO_COLORS.length] }}></span>
                                        <span className="truncate max-w-[180px]">{service.serviceName}</span>
                                     </td>
                                     <td className="text-right font-medium">{service.count}</td>
                                  </tr>
                                ))
                             ) : (
                                <tr><td colSpan={2} className="text-center text-gray-500 py-4">Tidak ada data</td></tr>
                             )}
                          </tbody>
                       </table>
                    </div>
                     {/* Pie Chart Container (Mengisi sisa ruang) */}
                     <div className="w-full h-64 lg:h-72">
                       {reportData.popularServices.length > 0 ? (
                         <div ref={popularServicesChartRef}>
                           <ResponsiveContainer width="100%" height="100%">
                             <PieChart>
                             <Pie
                               data={popularServicesPieData}
                               cx="50%"
                               cy="50%"
                               labelLine={false}
                               label={renderCustomizedLabel}
                               outerRadius="80%" // Buat radius lebih responsif
                               innerRadius="40%" // Buat donut chart
                               fill="#8884d8"
                               dataKey="value"
                               nameKey="name"
                               paddingAngle={2}
                             >
                               {popularServicesPieData.map((_, index) => (
                                 <Cell key={`cell-${index}`} fill={LOGO_COLORS[index % LOGO_COLORS.length]} stroke={theme === 'breaktime-dark' ? '#4b5563' : 'white'} strokeWidth={1}/>
                               ))}
                             </Pie>
                             <Tooltip formatter={(value: number, name: string) => [`${value} transaksi`, name]} />
                             {/* <Legend layout="vertical" align="right" verticalAlign="middle" iconSize={10} wrapperStyle={{fontSize: "12px"}}/> */}
                           </PieChart>
                         </ResponsiveContainer>
                         </div>
                       ) : (
                          <div className="flex items-center justify-center h-full text-gray-400 text-sm">Data Pie Chart Kosong</div>
                       )}
                     </div>
                  </div>
               </motion.div>
             )}

             {/* Frequent Customers - Hanya tampilkan jika bukan investor */}
             {!isInvestor && (
               <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow">
                  <h2 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-700"><FiUsers /> Pelanggan Tersering (Top 10)</h2>
                   <div className="overflow-x-auto max-h-96">
                       <table className="table table-sm w-full">
                         <thead>
                           <tr>
                             <th className="w-[30%]">Nama</th>
                             <th className="w-[30%]">Telepon</th>
                             <th className="text-right w-[15%]">Kunjungan</th>
                             <th className="text-right w-[25%]">Total Belanja</th>
                           </tr>
                         </thead>
                         <tbody>
                            {reportData.frequentCustomers.length > 0 ? (
                               reportData.frequentCustomers.map(cust => (
                                  <tr key={cust.customerId} className="hover:bg-gray-50">
                                     <td className="font-medium truncate">{cust.customerName}</td>
                                     <td className="truncate">{cust.customerPhone}</td>
                                     <td className="text-right">{cust.visitCount}</td>
                                     <td className="text-right font-semibold">{formatCurrency(cust.totalSpent)}</td>
                                  </tr>
                               ))
                             ) : (
                                <tr><td colSpan={4} className="text-center text-gray-500 py-4">Tidak ada data</td></tr>
                             )}
                         </tbody>
                      </table>
                   </div>
               </motion.div>
             )}

             {/* Therapist Performance - Hanya tampilkan jika bukan investor */}
             {!isInvestor && (
               <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow col-span-1 lg:col-span-2">
                  <h2 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-700"><FiUserCheck /> Kinerja Terapis (Top 10 by Sales)</h2>
                  <div className="w-full overflow-x-auto pb-2">
                     <div ref={therapistPerformanceChartRef} style={{ width: '100%', minWidth: '600px', height: '300px' }}>
                        <ResponsiveContainer>
                           <BarChart data={therapistPerformanceChartData} margin={{ top: 5, right: 10, left: -25, bottom: 5 }}>
                              <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0"/>
                              <XAxis dataKey="name" fontSize={10} interval={0} angle={-30} textAnchor="end" height={50} tick={{ fill: '#6b7280' }}/>
                              <YAxis fontSize={10} tickFormatter={(value) => new Intl.NumberFormat('id-ID', { notation: 'compact' }).format(value)} tick={{ fill: '#6b7280' }}/>
                              <Tooltip formatter={(value: number) => formatCurrency(value)} cursor={{ fill: '#f3f4f6' }}/>
                              <Legend wrapperStyle={{fontSize: "12px"}} verticalAlign="top" align="right"/>
                              <Bar dataKey="Penjualan" fill={LOGO_COLORS[0]} name="Total Penjualan" barSize={15} />
                              <Bar dataKey="Komisi" fill={LOGO_COLORS[1]} name="Total Komisi" barSize={15} />
                           </BarChart>
                        </ResponsiveContainer>
                     </div>
                  </div>
               </motion.div>
             )}

             {/* Kinerja Kasir */}
             <motion.div variants={fadeInUp} className="bg-white p-4 rounded-lg shadow col-span-1 lg:col-span-2">
                <h2 className="text-lg font-semibold mb-4 flex items-center gap-2 text-gray-700"><FiUser /> Kinerja Kasir</h2>
                <div className="mt-4">
                   <div className="overflow-x-auto">
                      <table className="table table-xs table-zebra w-full">
                         <thead>
                            <tr>
                               <th>Nama Kasir</th>
                               <th>Outlet</th>
                               <th className="text-right">Jumlah Transaksi</th>
                               <th className="text-right">Total Pendapatan</th>
                            </tr>
                         </thead>
                         <tbody>
                            {reportData?.cashierPerformance?.map((cashier) => (
                               <tr 
                                 key={`${cashier.cashierId}-${cashier.outletId}`}
                                 className="hover cursor-pointer"
                                 onClick={() => handleCashierClick(cashier)}
                                 title="Klik untuk melihat detail transaksi"
                               >
                                  <td>
                                    <div className="flex items-center gap-2">
                                      <span>{cashier.cashierName}</span>
                                      <FiEye className="text-gray-400 text-sm" />
                                    </div>
                                  </td>
                                  <td>{cashier.outletName}</td>
                                  <td className="text-right">{cashier.totalTransactions}</td>
                                  <td className="text-right">{formatCurrency(cashier.totalAmount)}</td>
                               </tr>
                            ))}
                            {!reportData?.cashierPerformance?.length && (
                               <tr>
                                  <td colSpan={4} className="text-center py-4">Tidak ada data kinerja kasir</td>
                               </tr>
                            )}
                         </tbody>
                      </table>
                   </div>
                </div>
             </motion.div>
          </div>
        </>
      )}

      {/* No Data State */}
       {!isLoading && !error && !reportData && (
          <motion.div variants={fadeInUp} className="text-center py-10 bg-white rounded-lg shadow">
             <FiBarChart2 className="mx-auto h-12 w-12 text-gray-400" />
             <h3 className="mt-2 text-lg font-medium text-gray-900">Data Laporan Kosong</h3>
             <p className="mt-1 text-sm text-gray-500">Silakan pilih filter di atas untuk melihat laporan.</p>
          </motion.div>
       )}

      {/* Modal Detail Jam Sibuk */}
      {showHourDetailModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
            <div className="p-4 border-b flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-800">
                Detail Transaksi Jam {selectedHour}:00
              </h3>
              <button
                onClick={() => setShowHourDetailModal(false)}
                className="btn btn-sm btn-ghost"
                type="button"
                aria-label="Tutup"
              >
                <FiX className="text-lg" />
              </button>
            </div>

            <div className="p-4 overflow-y-auto flex-grow">
              {isHourDetailLoading ? (
                <div className="flex justify-center items-center h-40">
                  <span className="loading loading-spinner loading-md text-primary"></span>
                </div>
              ) : hourDetailError ? (
                <div className="alert alert-error">
                  <p>{hourDetailError}</p>
                </div>
              ) : hourDetailData ? (
                <div className="space-y-6">
                  <div className="stats shadow w-full">
                    <div className="stat">
                      <div className="stat-title">Jam</div>
                      <div className="stat-value text-2xl">{hourDetailData.hour}:00</div>
                    </div>
                    <div className="stat">
                      <div className="stat-title">Jumlah Transaksi</div>
                      <div className="stat-value text-2xl">{hourDetailData.transactionCount}</div>
                    </div>
                  </div>

                  {/* Layanan yang terjadi pada jam tersebut */}
                  <div>
                    <h4 className="text-md font-semibold mb-2 text-gray-700">Layanan</h4>
                    <div className="overflow-x-auto">
                      <table className="table table-sm w-full">
                        <thead>
                          <tr>
                            <th>Nama Layanan</th>
                            <th className="text-right">Jumlah</th>
                            <th className="text-right">Total</th>
                          </tr>
                        </thead>
                        <tbody>
                          {hourDetailData.serviceStats.length > 0 ? (
                            hourDetailData.serviceStats.map((service) => (
                              <tr key={service.id}>
                                <td>{service.name}</td>
                                <td className="text-right">{service.count}</td>
                                <td className="text-right">{formatCurrency(service.totalAmount)}</td>
                              </tr>
                            ))
                          ) : (
                            <tr>
                              <td colSpan={3} className="text-center text-gray-500 py-4">
                                Tidak ada data layanan
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  </div>

                  {/* Detail transaksi */}
                  <div>
                    <h4 className="text-md font-semibold mb-2 text-gray-700">Detail Transaksi</h4>
                    <div className="overflow-x-auto">
                      <table className="table table-sm w-full">
                        <thead>
                          <tr>
                            <th>ID</th>
                            <th>Pelanggan</th>
                            <th>Terapis</th>
                            <th>Layanan</th>
                            <th className="text-right">Total</th>
                          </tr>
                        </thead>
                        <tbody>
                          {hourDetailData.transactions.length > 0 ? (
                            hourDetailData.transactions.map((transaction) => (
                              <tr key={transaction.id}>
                                <td>{transaction.displayId || transaction.id}</td>
                                <td>{transaction.customerName}</td>
                                <td>{transaction.therapistName}</td>
                                <td>
                                  <div className="flex flex-col gap-1">
                                    {transaction.services.map((service, idx) => (
                                      <span key={idx} className="text-xs">
                                        {service.quantity > 1 ? `${service.quantity}x ` : ''}{service.name}
                                      </span>
                                    ))}
                                  </div>
                                </td>
                                <td className="text-right">{formatCurrency(transaction.totalAmount)}</td>
                              </tr>
                            ))
                          ) : (
                            <tr>
                              <td colSpan={5} className="text-center text-gray-500 py-4">
                                Tidak ada data transaksi
                              </td>
                            </tr>
                          )}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-10">
                  <p className="text-gray-500">Tidak ada data detail jam</p>
                </div>
              )}
            </div>

            <div className="p-4 border-t flex justify-end">
              <button
                onClick={() => setShowHourDetailModal(false)}
                className="btn btn-primary"
                type="button"
              >
                Tutup
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Detail Transaksi Kasir */}
      {showCashierDetailModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-5xl max-h-[90vh] overflow-hidden flex flex-col">
            <div className="p-4 border-b flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-800">
                Detail Transaksi {selectedCashier?.cashierName}
              </h3>
              <button
                onClick={() => setShowCashierDetailModal(false)}
                className="btn btn-sm btn-ghost"
                type="button"
                aria-label="Tutup"
              >
                <FiX className="text-lg" />
              </button>
            </div>

            <div className="p-4 overflow-y-auto flex-grow">
              {isCashierDetailLoading ? (
                <div className="flex justify-center items-center h-40">
                  <span className="loading loading-spinner loading-md text-primary"></span>
                </div>
              ) : cashierDetailError ? (
                <div className="alert alert-error">
                  <p>{cashierDetailError}</p>
                </div>
              ) : cashierDetailData ? (
                <div className="space-y-6">
                  {/* Summary Cards */}
                  <div className="stats shadow w-full">
                    <div className="stat">
                      <div className="stat-title">Nama Kasir</div>
                      <div className="stat-value text-lg">{cashierDetailData.cashierName}</div>
                      <div className="stat-desc">{cashierDetailData.outletName}</div>
                    </div>
                    <div className="stat">
                      <div className="stat-title">Total Transaksi</div>
                      <div className="stat-value text-2xl">{cashierDetailData.totalTransactions}</div>
                      <div className="stat-desc">transaksi</div>
                    </div>
                    <div className="stat">
                      <div className="stat-title">Total Pendapatan</div>
                      <div className="stat-value text-xl">{formatCurrency(cashierDetailData.totalAmount)}</div>
                      <div className="stat-desc">dari periode ini</div>
                    </div>
                  </div>

                  {/* Detail Transaksi */}
                  <div>
                    <h4 className="text-md font-semibold mb-4 text-gray-700">
                      Detail Transaksi ({cashierDetailData.transactions.length} transaksi)
                    </h4>
                    <div className="overflow-x-auto">
                      <table className="table table-sm w-full">
                        <thead>
                          <tr>
                            <th>ID Transaksi</th>
                            <th>Tanggal</th>
                            <th>Pelanggan</th>
                            <th>Terapis</th>
                            <th>Layanan</th>
                            <th>Metode Bayar</th>
                            <th className="text-right">Total</th>
                          </tr>
                        </thead>
                        <tbody>
                          {cashierDetailData.transactions.length > 0 ? (
                            cashierDetailData.transactions.map((transaction) => (
                              <tr key={transaction.id}>
                                <td className="font-mono text-xs">
                                  {transaction.displayId || transaction.id}
                                </td>
                                <td className="text-xs">
                                  {format(new Date(transaction.transactionDate), 'dd/MM/yyyy HH:mm', { locale: idLocale })}
                                </td>
                                <td className="text-sm">{transaction.customerName}</td>
                                <td className="text-sm">{transaction.therapistName}</td>
                                <td>
                                  <div className="flex flex-col gap-1">
                                    {transaction.services.map((service, idx) => (
                                      <span key={idx} className="text-xs">
                                        {service.quantity > 1 ? `${service.quantity}x ` : ''}{service.name}
                                      </span>
                                    ))}
                                  </div>
                                </td>
                                <td className="text-xs">
                                  <span className="badge badge-sm badge-outline">
                                    {transaction.paymentMethod}
                                  </span>
                                </td>
                                <td className="text-right font-medium">{formatCurrency(transaction.totalAmount)}</td>
                              </tr>
                            ))
                          ) : (
                            <tr>
                              <td colSpan={7} className="text-center text-gray-500 py-8">
                                Tidak ada data transaksi untuk kasir ini
                              </td>
                            </tr>
                          )}
                        </tbody>
                        {cashierDetailData.transactions.length > 0 && (
                          <tfoot>
                            <tr className="font-bold bg-base-200">
                              <td colSpan={6} className="text-right">Total:</td>
                              <td className="text-right">{formatCurrency(cashierDetailData.totalAmount)}</td>
                            </tr>
                          </tfoot>
                        )}
                      </table>
                    </div>
                  </div>

                  {/* Footer Info */}
                  <div className="text-sm text-gray-500 bg-base-100 p-4 rounded-lg">
                    <p>📊 <strong>Statistik:</strong> Menampilkan semua transaksi yang dibuat oleh {cashierDetailData.cashierName} 
                      {selectedCashier?.outletName && ` di outlet ${selectedCashier.outletName}`} 
                      untuk periode {formatDateDisplay(startDate)} - {formatDateDisplay(endDate)}</p>
                  </div>
                </div>
              ) : (
                <div className="text-center py-10">
                  <p className="text-gray-500">Tidak ada data detail kasir</p>
                </div>
              )}
            </div>

            <div className="p-4 border-t flex justify-end gap-2">
              <button
                onClick={() => setShowCashierDetailModal(false)}
                className="btn btn-primary"
                type="button"
              >
                Tutup
              </button>
            </div>
          </div>
        </div>
      )}

    </motion.div>
  );
}