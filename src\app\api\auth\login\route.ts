import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import * as bcrypt from 'bcrypt';
import * as jwt from 'jsonwebtoken';

// Gunakan variabel environment dan pastikan ada nilai default yang aman
// Pastikan nilai ini sama di seluruh file autentikasi
const JWT_SECRET = process.env.JWT_SECRET || 'breaktime_session_secret_key_for_jwt_auth';

// Handler OPTIONS untuk CORS pre-flight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Max-Age': '86400',
    },
  });
}

export async function POST(request: Request) {
  let response;
  try {
    // Log untuk debugging
    console.log('Login request received');

    let username, password;
    try {
      const body = await request.json();
      username = body.username;
      password = body.password;
      console.log('Login attempt for username:', username ? username.substring(0, 3) + '***' : 'undefined');
    } catch (e) {
      console.error('Error parsing request body:', e);
      response = NextResponse.json(
        { error: 'Format permintaan tidak valid' },
        { status: 400 }
      );
      return addCorsHeaders(response);
    }

    // Cek apakah username dan password disediakan
    if (!username || !password) {
      response = NextResponse.json(
        { error: 'Username dan password diperlukan' },
        { status: 400 }
      );
      return addCorsHeaders(response);
    }

    // Ambil user dari database beserta permission
    const user = await prisma.user.findUnique({
      where: {
        username: username,
      },
      include: {
        permissions: true, // Include permissions
      },
    });

    // Jika user tidak ditemukan
    if (!user) {
      response = NextResponse.json(
        { error: 'User tidak ditemukan' },
        { status: 404 }
      );
      return addCorsHeaders(response);
    }

    // Cek apakah user aktif
    if (!user.isActive) {
      response = NextResponse.json(
        { error: 'Akun tidak aktif' },
        { status: 403 }
      );
      return addCorsHeaders(response);
    }

    // Bandingkan password
    const passwordMatch = await bcrypt.compare(password, user.password);

    if (!passwordMatch) {
      response = NextResponse.json(
        { error: 'Password salah' },
        { status: 401 }
      );
      return addCorsHeaders(response);
    }

    // Generate token dengan permission
    const token = jwt.sign(
      {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        permissions: user.permissions || [] // Sertakan permission dalam token
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    // Update lastLoginAt
    await prisma.user.update({
      where: {
        id: user.id,
      },
      data: {
        lastLoginAt: new Date(),
      },
    });

    // Exclude password dari response, include permissions
    const userResponse = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      isActive: user.isActive,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      lastLoginAt: user.lastLoginAt,
      permissions: user.permissions || [] // Sertakan permission dalam response
    };

    // Buat response dengan cookie
    response = NextResponse.json({
      message: 'Login berhasil',
      user: userResponse,
      token: token
    });

    // Set cookie untuk user token
    response.cookies.set({
      name: 'user_token',
      value: token,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 86400, // 24 jam dalam detik
      path: '/',
      sameSite: 'lax', // Tambahkan sameSite policy untuk keamanan
    });

    return addCorsHeaders(response);
  } catch (error) {
    console.error('Login error:', error);
    response = NextResponse.json(
      { error: 'Terjadi kesalahan saat login' },
      { status: 500 }
    );
    return addCorsHeaders(response);
  }
}

// Helper untuk menambahkan header CORS
function addCorsHeaders(response: NextResponse) {
  // Set headers untuk CORS
  response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set('Access-Control-Allow-Methods', 'POST, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  response.headers.set('Access-Control-Allow-Credentials', 'true');
  return response;
}