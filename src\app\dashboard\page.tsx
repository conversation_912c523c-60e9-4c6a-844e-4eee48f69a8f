'use client'; // Jika perlu animasi atau interaksi

import { useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import { useOutletContext } from '@/contexts/OutletContext';
import { toast } from 'react-hot-toast';
import {
  FiCalendar, FiDollarSign, FiUserCheck, FiClock, FiUsers, FiZap, FiArrowRight, FiBriefcase, FiAlertTriangle, FiActivity,
  FiCheckCircle, FiXCircle, FiDownload, FiChevronDown, FiShoppingCart
} from 'react-icons/fi';
import Link from 'next/link';
import {
  XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area
} from 'recharts';
import resolveConfig from 'tailwindcss/resolveConfig'; // Untuk mengambil warna tema
import tailwindConfig from '../../../tailwind.config.js'; // Sesuaikan path jika perlu
import * as XLSX from 'xlsx';
import { jsPDF } from "jspdf";
import autoTable from 'jspdf-autotable';

// Resolve Tailwind config untuk mendapatkan warna tema
const fullConfig = resolveConfig(tailwindConfig);

// Varian animasi
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
};

// Tipe data Chart
type ChartDataPoint = {
  label: string; // Bisa jam, hari, minggu, bulan
  bookings: number;
  transactions: number; // Tambahkan jumlah transaksi
};

// Tipe data mock Dashboard
type StatData = {
  bookingsToday: number;
  revenueToday: number;
  availableTherapists: number;
  totalGuestsToday: number;
  transactionsToday: number; // Tambahkan total transaksi hari ini
};

// Tipe Baru
type TopService = {
  name: string;
  count: number; // Ubah nama field agar lebih generik (bisa booking/transaksi)
};

type TopTherapist = {
  name: string;
  avatarUrl?: string; // Opsional
  bookingCount: number; // Jumlah booking
  transactionCount: number; // Jumlah transaksi
};

// Update DashboardData
type DashboardData = {
  stats: StatData;
  upcomingBookings: UpcomingBooking[];
  peakHours: string[];
  hourlyBookings: ChartDataPoint[];
  topServices: TopService[];
  topTherapists: TopTherapist[];
};

type UpcomingBooking = {
  id: string;
  time: string;
  customerName: string;
  therapistName: string;
  serviceName: string; // Ganti dari service
  status: string; // Tambahkan status booking
};

// Tipe data dari API booking
type ApiBooking = {
  id: string;
  bookingDate: string;
  bookingTime: string;
  status: string;
  customer: { id: string; name: string };
  therapist: { id: string; name: string };
  services: { id: string; name: string; price: number }[];
  // ... potentially other fields
};

// Tipe data dari API transaction
type ApiTransaction = {
    id: string;
    bookingId: string;
    outletId: string;
    customerId: string;
    therapistId: string;
    status: string; // PENDING, COMPLETED, FAILED, REFUNDED
    totalAmount: number;
    paymentMethod?: string | null;
    createdAt: string;
    updatedAt: string;
    customer: { id: string; name: string };
    therapist: { id: string; name: string };
    booking?: { // Include booking details linked to transaction (optional)
        id: string;
        bookingTime: string;
        bookingDate: string;
        services: { id: string; name: string; price: number }[];
    };
    outlet: { id: string; name: string };
    // Tambahkan properti untuk items transaksi
    transactionItems?: {
        service: { id: string; name: string; price: number };
        quantity: number;
        price: number;
    }[];
    // Format alternatif untuk items
    items?: {
        id?: string;
        name: string;
        price: number;
        quantity: number;
    }[];
};

// Opsi Periode Chart
type ChartPeriod = 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
const chartPeriods: { key: ChartPeriod; label: string }[] = [
  { key: 'daily', label: 'Harian' },
  { key: 'weekly', label: 'Mingguan' },
  { key: 'monthly', label: 'Bulanan' },
  { key: 'quarterly', label: '3 Bulan' },
  { key: 'yearly', label: 'Tahunan' },
];

export default function DashboardOverviewPage() {
  const { selectedOutletId } = useOutletContext();
  const [outletName, setOutletName] = useState<string>('');
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]); // State baru untuk data chart
  const [selectedPeriod, setSelectedPeriod] = useState<ChartPeriod>('daily'); // State untuk periode
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear()); // State untuk tahun yang dipilih
  const [isLoading, setIsLoading] = useState(true);
  const [isChartLoading, setIsChartLoading] = useState(true); // State loading spesifik untuk chart
  const [error, setError] = useState<string | null>(null); // State untuk error
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancelBookingId, setCancelBookingId] = useState<string>('');
  const [cancelBookingNumber, setCancelBookingNumber] = useState<string>('');
  const [upcomingBookings, setUpcomingBookings] = useState<UpcomingBooking[]>([]);
  const [showDownloadOptions, setShowDownloadOptions] = useState(false);
  // State untuk menyimpan total per jenis pembayaran
  const [paymentMethodTotals, setPaymentMethodTotals] = useState<{[key: string]: number}>({});
  // State untuk menyimpan total per jenis pembayaran berdasarkan periode
  const [periodPaymentMethodTotals, setPeriodPaymentMethodTotals] = useState<{[key: string]: number}>({});

  // Memoize warna tema
  const currentThemeColors = useMemo(() => ({
    primary: fullConfig.theme?.colors?.primary || '#F4BB45', // Fallback jika config belum terbaca
    secondary: fullConfig.theme?.colors?.secondary || '#4E9E97',
  }), []);

  // Fungsi untuk menampilkan modal konfirmasi pembatalan
  const handleShowCancelModal = (bookingId: string) => {
    // Ambil 6 karakter pertama dari ID booking untuk tampilan yang lebih singkat
    const shortBookingNumber = bookingId.substring(0, 6).toUpperCase();
    setCancelBookingId(bookingId);
    setCancelBookingNumber(shortBookingNumber);
    setShowCancelModal(true);
  };

  // Fungsi untuk menangani konfirmasi pembatalan
  const handleConfirmCancel = async () => {
    if (!cancelBookingId) return;

    setIsLoading(true);
    setShowCancelModal(false);

    try {
      const response = await fetch(`/api/bookings/${cancelBookingId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'CANCELLED' }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Gagal membatalkan booking`);
      }

      // Update upcomingBookings secara realtime tanpa perlu fetch ulang
      setUpcomingBookings(prevBookings => {
        // Hapus booking yang dibatalkan dari daftar
        return prevBookings.filter(booking => booking.id !== cancelBookingId);
      });

      setIsLoading(false);
      toast.success(`Booking #${cancelBookingNumber} berhasil dibatalkan.`);
    } catch (error) {
      toast.error(`Gagal membatalkan booking: ${error instanceof Error ? error.message : 'Silakan coba lagi.'}`);
      setIsLoading(false);
    }

    // Reset state
    setCancelBookingId('');
    setCancelBookingNumber('');
  };

  // Fungsi untuk memperbarui status booking
  const handleUpdateBookingStatus = async (bookingId: string, newStatus: string) => {
    // Validasi string status
    const validStatuses = ['PENDING', 'CONFIRMED', 'COMPLETED', 'CANCELLED', 'NO_SHOW'];
    if (!validStatuses.includes(newStatus)) {
      toast.error("Status baru tidak valid.");
      return;
    }

    // Jika status CANCELLED, gunakan modal (sudah ditangani di handleConfirmCancel)
    if (newStatus === 'CANCELLED') {
      handleShowCancelModal(bookingId);
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch(`/api/bookings/${bookingId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Gagal memperbarui status booking ke ${newStatus}`);
      }

      // Update upcomingBookings secara realtime tanpa perlu fetch ulang
      setUpcomingBookings(prevBookings => {
        // Jika status CANCELLED, hapus booking dari daftar
        if (newStatus === 'CANCELLED') {
          return prevBookings.filter(booking => booking.id !== bookingId);
        }

        // Jika status lain (CONFIRMED), update status booking
        return prevBookings.map(booking => {
          if (booking.id === bookingId) {
            return { ...booking, status: newStatus };
          }
          return booking;
        });
      });

      setIsLoading(false);
      // Ambil 6 karakter pertama dari ID booking untuk tampilan yang lebih singkat
      const shortBookingNumber = bookingId.substring(0, 6).toUpperCase();
      toast.success(`Status booking #${shortBookingNumber} berhasil diubah menjadi ${newStatus}.`);
    } catch (error) {
      toast.error(`Gagal memperbarui status booking: ${error instanceof Error ? error.message : 'Silakan coba lagi.'}`);
      setIsLoading(false);
    }
  };

  // Efek untuk memuat data dashboard utama
  useEffect(() => {
    if (selectedOutletId !== null) {
      setIsLoading(true);
      setError(null); // Reset error setiap kali fetch

      // Fungsi untuk mengambil data dari API
      const fetchDashboardData = async () => {
        try {
          // Ambil data outlet untuk mendapatkan nama outlet
          const outletResponse = await fetch(`/api/outlets/${selectedOutletId}`);
          if (outletResponse.ok) {
            const outletData = await outletResponse.json();
            setOutletName(outletData.outlet?.name || 'Outlet Tidak Diketahui');
          } else {
             setOutletName('Outlet Tidak Diketahui');
          }

          // Ambil data booking hari ini
          // Tambahkan timestamp untuk memastikan tidak ada cache
          const timestamp = new Date().getTime();
          // Gunakan tanggal lokal untuk menghindari masalah zona waktu
          const today = new Date();
          // Format tanggal dalam YYYY-MM-DD menggunakan tanggal lokal, bukan UTC
          const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

          // Tambahkan logging untuk debugging zona waktu
          console.log('Mengambil data untuk tanggal:', todayStr);
          console.log('Zona waktu browser:', Intl.DateTimeFormat().resolvedOptions().timeZone);
          console.log('Waktu lokal:', today.toString());
          console.log('Waktu UTC:', today.toUTCString());

          // Gunakan startDate dan endDate sebagai alternatif jika parameter date tidak berfungsi di production
          // Tambahkan parameter debug=true untuk memicu logging tambahan di API
          const bookingsApiUrl = `/api/bookings?outletId=${selectedOutletId}&date=${todayStr}&startDate=${todayStr}&endDate=${todayStr}&debug=true&_nocache=${timestamp}`;

          console.log('Dashboard - Calling bookings API with parameters:', {
            outletId: selectedOutletId,
            date: todayStr,
            startDate: todayStr,
            endDate: todayStr,
            timestamp
          });
          console.log('URL API bookings:', bookingsApiUrl);
          const bookingsResponse = await fetch(bookingsApiUrl);

          if (!bookingsResponse.ok) {
            const errorData = await bookingsResponse.json().catch(() => ({}));
            throw new Error(errorData.error || 'Gagal memuat data booking');
          }

          const bookingsData = await bookingsResponse.json();
          console.log('Respons API bookings:', bookingsData);
          let todayBookings: ApiBooking[] = bookingsData.bookings || []; // Akses array booking
          console.log('Jumlah booking hari ini:', todayBookings.length);

          // Jika tidak ada booking ditemukan, coba fallback ke API alternatif
          if (todayBookings.length === 0) {
            console.log('Dashboard - No bookings found, trying fallback API...');
            try {
              // Gunakan API dashboard/bookings sebagai fallback
              const fallbackApiUrl = `/api/dashboard/bookings?outletId=${selectedOutletId}&fromDate=${todayStr}&toDate=${todayStr}&_nocache=${timestamp}`;
              console.log('Dashboard - Fallback API URL:', fallbackApiUrl);

              const fallbackResponse = await fetch(fallbackApiUrl);
              if (fallbackResponse.ok) {
                const fallbackData = await fallbackResponse.json();
                console.log('Dashboard - Fallback API response:', fallbackData);

                // Jika fallback berhasil dan mengembalikan data
                if (Array.isArray(fallbackData) && fallbackData.length > 0) {
                  todayBookings = fallbackData;
                  console.log('Dashboard - Using fallback bookings:', todayBookings.length);
                }
              }
            } catch (fallbackError) {
              console.error('Dashboard - Fallback API failed:', fallbackError);
            }
          }

          // Hitung statistik dari data booking
          const totalBookings = todayBookings.length;
          let totalRevenue = 0;
          let totalTransactions = 0; // Tambahkan variabel untuk menghitung total transaksi
          const uniqueGuests = new Set<string>();

          // Ambil data booking yang akan datang (PENDING atau CONFIRMED)
          const upcomingBookingsData: UpcomingBooking[] = todayBookings
            .filter((booking: ApiBooking) => booking.status === 'PENDING' || booking.status === 'CONFIRMED')
            .map((booking: ApiBooking) => ({
              id: booking.id,
              time: booking.bookingTime,
              customerName: booking.customer?.name || 'Pelanggan',
              therapistName: booking.therapist?.name || 'Terapis',
              serviceName: booking.services?.[0]?.name || 'Layanan', // Ambil nama layanan pertama
              status: booking.status // Tambahkan status booking
            }));

          // Set upcomingBookings state terpisah untuk memudahkan update realtime
          setUpcomingBookings(upcomingBookingsData);

          // API dari inspeksi kode tidak menggunakan parameter 'date' tapi menggunakan parameter 'startDate' dan 'endDate'
          // Ambil data transaksi hari ini untuk menghitung pendapatan
          // Kita gunakan startDate dan endDate dg nilai yang sama untuk mendapatkan transaksi hari ini saja
          const transactionsResponse = await fetch(
            `/api/transactions?outletId=${selectedOutletId}&startDate=${todayStr}&endDate=${todayStr}&status=COMPLETED&_nocache=${timestamp}`
          );

          if (transactionsResponse.ok) {
            const transactionsData = await transactionsResponse.json();
            console.log('Data transaksi hari ini:', transactionsData);
            const todayTransactions: ApiTransaction[] = transactionsData.transactions || [];

            // Log untuk debug
            console.log(`Jumlah transaksi hari ini (${todayStr}):`, todayTransactions.length);

            // Filter tambahan di client-side untuk memastikan hanya transaksi hari ini
            // Gunakan tanggal lokal untuk menghindari masalah zona waktu
            const startOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0);
            const endOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);

            console.log('Filter transaksi dari:', startOfToday, 'sampai:', endOfToday);

            // Filter transaksi berdasarkan tanggal
            const filteredTransactions = todayTransactions.filter(transaction => {
              const transactionDate = new Date(transaction.createdAt);
              const isToday = transactionDate >= startOfToday && transactionDate <= endOfToday;
              console.log(`Transaksi ${transaction.id}, tanggal ${transactionDate}, isToday: ${isToday}`);
              return isToday;
            });

            console.log('Jumlah transaksi setelah filter tanggal:', filteredTransactions.length);

            // Hitung total pendapatan dan jumlah transaksi yang sudah difilter
            totalTransactions = filteredTransactions.length; // Set jumlah transaksi

            // Inisialisasi objek untuk menyimpan total per jenis pembayaran
            const paymentTotals: {[key: string]: number} = {};

            filteredTransactions.forEach((transaction) => {
              totalRevenue += transaction.totalAmount || 0;

              // Tambahkan customer ID ke set uniqueGuests
              if (transaction.customer?.id) {
                uniqueGuests.add(transaction.customer.id);
              }

              // Hitung total per jenis pembayaran
              const paymentMethod = transaction.paymentMethod || 'UNKNOWN';
              if (!paymentTotals[paymentMethod]) {
                paymentTotals[paymentMethod] = 0;
              }
              paymentTotals[paymentMethod] += transaction.totalAmount || 0;

              // Jika metode pembayaran adalah SPLIT, tambahkan ke total masing-masing metode
              if (paymentMethod === 'SPLIT' && transaction.splitPayment) {
                const firstMethod = transaction.splitPayment.firstMethod || 'UNKNOWN';
                const secondMethod = transaction.splitPayment.secondMethod || 'UNKNOWN';

                // Asumsikan pembagian 50/50 jika tidak ada informasi lebih detail
                const halfAmount = (transaction.totalAmount || 0) / 2;

                if (!paymentTotals[firstMethod]) {
                  paymentTotals[firstMethod] = 0;
                }
                if (!paymentTotals[secondMethod]) {
                  paymentTotals[secondMethod] = 0;
                }

                paymentTotals[firstMethod] += halfAmount;
                paymentTotals[secondMethod] += halfAmount;
              }
            });

            // Set state untuk total per jenis pembayaran
            setPaymentMethodTotals(paymentTotals);

            console.log('Total pendapatan hari ini:', totalRevenue);
            console.log('Total transaksi hari ini:', totalTransactions);
            console.log('Total per jenis pembayaran:', paymentTotals);
          } else {
            console.error('Gagal memuat data transaksi hari ini');
          }

          // Tambahkan tamu unik dari booking juga
          todayBookings.forEach((booking: ApiBooking) => {
            if (booking.customer?.id) {
              uniqueGuests.add(booking.customer.id);
            }
          });

          // Ambil data terapis yang tersedia
          const therapistsResponse = await fetch(`/api/therapists?outletId=${selectedOutletId}&isActive=true`);
          let availableTherapists = 0;

          if (therapistsResponse.ok) {
            const therapistsData = await therapistsResponse.json();
            availableTherapists = therapistsData.therapists?.length || 0;
          } // Tambahkan error handling jika perlu

          // Set data dashboard
        setDashboardData({
            stats: {
              bookingsToday: totalBookings,
              revenueToday: totalRevenue,
              availableTherapists: availableTherapists,
              totalGuestsToday: uniqueGuests.size,
              transactionsToday: totalTransactions // Tambahkan total transaksi
            },
            upcomingBookings: [], // Tidak digunakan lagi, kita menggunakan state upcomingBookings terpisah
            peakHours: [], // Akan dihitung di useEffect lain
            topServices: [], // Akan dihitung di useEffect lain
            topTherapists: [], // Akan dihitung di useEffect lain
            hourlyBookings: [] // Pastikan ini ditambahkan
          });
        } catch (error) {
          setError(error instanceof Error ? error.message : 'Terjadi kesalahan saat memuat data dashboard');
          setDashboardData(null); // Set data jadi null jika error
        } finally {
        setIsLoading(false);
        }
      };

      fetchDashboardData();
    }
  }, [selectedOutletId]);

  // Efek terpisah untuk memuat data chart dan data rangking berdasarkan periode
  useEffect(() => {
    if (selectedOutletId !== null && !isLoading) {
      setIsChartLoading(true);

      const fetchChartAndRankingData = async () => {
        try {
          // Tentukan rentang tanggal berdasarkan periode
          const today = new Date();
          let fromDate = new Date(today);
          let toDate = new Date(today); // Sampai hari ini

          // KEMBALIKAN BLOK INI
          switch (selectedPeriod) {
             case 'weekly':
               fromDate.setDate(today.getDate() - today.getDay() + (today.getDay() === 0 ? -6 : 1)); // Mulai Senin minggu ini
               break;
             case 'monthly':
               fromDate = new Date(today.getFullYear(), today.getMonth(), 1); // Mulai tanggal 1 bulan ini
               break;
             case 'quarterly':
               fromDate = new Date(today.getFullYear(), Math.floor(today.getMonth() / 3) * 3, 1); // Mulai bulan pertama kuartal ini
               break;
             case 'yearly':
               // Mulai 1 Januari tahun yang dipilih
               fromDate = new Date(selectedYear, 0, 1);
               toDate = new Date(selectedYear, 11, 31); // Akhir tahun yang dipilih (31 Desember)
               break;
             case 'daily':
             default:
                // fromDate already set to today
               break;
           }

          // KEMBALIKAN BLOK INI
          const fromDateStr = fromDate.toISOString().split('T')[0];
          const toDateStr = toDate.toISOString().split('T')[0];

          // Fetch data booking DAN transaksi untuk periode tersebut
          const bookingsApiUrl = `/api/bookings?outletId=${selectedOutletId}&startDate=${fromDateStr}&endDate=${toDateStr}`;
          const transactionsApiUrl = `/api/transactions?outletId=${selectedOutletId}&startDate=${fromDateStr}&endDate=${toDateStr}&status=COMPLETED`; // Hanya transaksi completed

          const [bookingsResponse, transactionsResponse] = await Promise.all([
             fetch(bookingsApiUrl),
             fetch(transactionsApiUrl)
          ]);

          // Periksa respons booking
          if (!bookingsResponse.ok) {
            throw new Error(`Gagal memuat data booking periodik (Status: ${bookingsResponse.status})`);
          }

          // Periksa respons transaksi
          if (!transactionsResponse.ok) {
            throw new Error(`Gagal memuat data transaksi periodik (Status: ${transactionsResponse.status})`);
          }

          // Hanya lanjutkan jika keduanya berhasil
          const bookingsData = await bookingsResponse.json();
          const transactionsData = await transactionsResponse.json(); // Ini seharusnya sudah benar sekarang

          // Log untuk debugging
          console.log('Transactions API response:', transactionsData);
          if (transactionsData.transactions && transactionsData.transactions.length > 0) {
            console.log('Sample transaction structure:', transactionsData.transactions[0]);
            if (transactionsData.transactions[0].items) {
              console.log('Sample transaction items:', transactionsData.transactions[0].items);
            }
            if (transactionsData.transactions[0].transactionItems) {
              console.log('Sample transaction transactionItems:', transactionsData.transactions[0].transactionItems);
            }
          }

          const periodBookings: ApiBooking[] = bookingsData.bookings || [];
          const periodTransactions: ApiTransaction[] = transactionsData.transactions || [];

          let newChartData: ChartDataPoint[] = [];
          let newPeakHours: string[] = [];
          let newTopServices: TopService[] = [];
          let newTopTherapists: TopTherapist[] = [];

          // --- Hitung Top Services & Top Therapists ---
          const serviceCounts: Record<string, number> = {};
          const therapistCounts: Record<string, { name: string; bookingCount: number; transactionCount: number }> = {};

          // Hitung dari booking
          periodBookings.forEach((booking) => {
            // Hitung layanan dari booking
            booking.services?.forEach((service) => {
              if (service.name) {
                serviceCounts[service.name] = (serviceCounts[service.name] || 0) + 1;
              }
            });
            // Hitung terapis dari booking
            if (booking.therapist?.id && booking.therapist?.name) {
              if (!therapistCounts[booking.therapist.id]) {
                therapistCounts[booking.therapist.id] = {
                  name: booking.therapist.name,
                  bookingCount: 0,
                  transactionCount: 0
                };
              }
              therapistCounts[booking.therapist.id].bookingCount++;
            }
          });

          // Inisialisasi objek untuk menyimpan total per jenis pembayaran berdasarkan periode
          const periodPaymentTotals: {[key: string]: number} = {};

          // Hitung dari transaksi
          periodTransactions.forEach((transaction) => {
            // Hitung layanan dari transaksi - dari booking jika ada
            if (transaction.booking?.services) {
              transaction.booking.services.forEach((service) => {
                if (service.name) {
                  serviceCounts[service.name] = (serviceCounts[service.name] || 0) + 1;
                }
              });
            }

            // Hitung layanan dari transactionItems (ini untuk transaksi langsung tanpa booking)
            if (transaction.transactionItems && transaction.transactionItems.length > 0) {
              transaction.transactionItems.forEach((item) => {
                if (item.service?.name) {
                  serviceCounts[item.service.name] = (serviceCounts[item.service.name] || 0) + 1;
                }
              });
            }

            // Hitung layanan dari items (format alternatif yang mungkin digunakan)
            if (transaction.items && transaction.items.length > 0) {
              transaction.items.forEach((item) => {
                if (item.name) {
                  serviceCounts[item.name] = (serviceCounts[item.name] || 0) + 1;
                }
              });
            }

            // Hitung terapis dari transaksi
            if (transaction.therapist?.id && transaction.therapist?.name) {
              if (!therapistCounts[transaction.therapist.id]) {
                therapistCounts[transaction.therapist.id] = {
                  name: transaction.therapist.name,
                  bookingCount: 0,
                  transactionCount: 0
                };
              }
              therapistCounts[transaction.therapist.id].transactionCount++;
            }

            // Hitung total per jenis pembayaran berdasarkan periode
            const paymentMethod = transaction.paymentMethod || 'UNKNOWN';
            if (!periodPaymentTotals[paymentMethod]) {
              periodPaymentTotals[paymentMethod] = 0;
            }
            periodPaymentTotals[paymentMethod] += transaction.totalAmount || 0;

            // Jika metode pembayaran adalah SPLIT, tambahkan ke total masing-masing metode
            if (paymentMethod === 'SPLIT' && transaction.splitPayment) {
              const firstMethod = transaction.splitPayment.firstMethod || 'UNKNOWN';
              const secondMethod = transaction.splitPayment.secondMethod || 'UNKNOWN';

              // Asumsikan pembagian 50/50 jika tidak ada informasi lebih detail
              const halfAmount = (transaction.totalAmount || 0) / 2;

              if (!periodPaymentTotals[firstMethod]) {
                periodPaymentTotals[firstMethod] = 0;
              }
              if (!periodPaymentTotals[secondMethod]) {
                periodPaymentTotals[secondMethod] = 0;
              }

              periodPaymentTotals[firstMethod] += halfAmount;
              periodPaymentTotals[secondMethod] += halfAmount;
            }
          });

          // Set state untuk total per jenis pembayaran berdasarkan periode
          setPeriodPaymentMethodTotals(periodPaymentTotals);

          // Log untuk debugging
          console.log('Service counts before sorting:', serviceCounts);

          // Urutkan dan ambil top 5
          newTopServices = Object.entries(serviceCounts)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 5)
            .map(([name, count]) => ({ name, count }));

          console.log('Top services after sorting:', newTopServices);

          newTopTherapists = Object.values(therapistCounts)
            .sort((a, b) => {
              // Urutkan berdasarkan jumlah total (booking + transaksi)
              const totalA = a.bookingCount + a.transactionCount;
              const totalB = b.bookingCount + b.transactionCount;
              return totalB - totalA;
            })
            .slice(0, 5)
            .map(t => ({
              name: t.name,
              bookingCount: t.bookingCount,
              transactionCount: t.transactionCount
            }));
          // --- End Hitung Top ---

          // --- Buat data chart (Booking & Transaksi) ---
          switch (selectedPeriod) {
            case 'weekly': {
            const days = ['Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab', 'Min'];
              // Initialize dengan objek lengkap
              const dayCounts = Array(7).fill(0).map(() => ({ bookings: 0, transactions: 0 }));

              // Pastikan periodBookings adalah array dan tidak undefined
              if (Array.isArray(periodBookings) && periodBookings.length > 0) {
                periodBookings.forEach((booking) => {
                  try {
                    if (booking && booking.bookingDate) {
                      const bookingDate = new Date(booking.bookingDate + 'T00:00:00');
                      if (!isNaN(bookingDate.getTime())) {
                        const dayIndex = (bookingDate.getDay() + 6) % 7;
                        if (dayIndex >= 0 && dayIndex < 7) {
                          dayCounts[dayIndex].bookings++;
                        }
                      }
                    }
                  } catch (error) {
                    // Abaikan error parsing tanggal
                  }
                });
              }

              // Pastikan periodTransactions adalah array dan tidak undefined
              if (Array.isArray(periodTransactions) && periodTransactions.length > 0) {
                periodTransactions.forEach((transaction) => {
                  try {
                    if (transaction && transaction.createdAt) {
                      const transactionDate = new Date(transaction.createdAt);
                      if (!isNaN(transactionDate.getTime())) {
                        const dayIndex = (transactionDate.getDay() + 6) % 7;
                        if (dayIndex >= 0 && dayIndex < 7) {
                          dayCounts[dayIndex].transactions++;
                        }
                      }
                    }
                  } catch (error) {
                    // Abaikan error parsing tanggal
                  }
                });
              }

              newChartData = days.map((day, index) => ({
              label: day,
                bookings: dayCounts[index].bookings,
                transactions: dayCounts[index].transactions // Pastikan transactions ada
            }));
            break;
            }
            case 'monthly': {
              // Initialize dengan objek lengkap
              const weeks = Array(5).fill(0).map(() => ({ bookings: 0, transactions: 0 }));

              // Pastikan periodBookings adalah array dan tidak undefined
              if (Array.isArray(periodBookings) && periodBookings.length > 0) {
                periodBookings.forEach((booking) => {
                  try {
                    if (booking && booking.bookingDate) {
                      const bookingDate = new Date(booking.bookingDate + 'T00:00:00');
                      if (!isNaN(bookingDate.getTime())) {
                        const weekIndex = Math.floor((bookingDate.getDate() - 1) / 7);
                        if (weekIndex >= 0 && weekIndex < 5) {
                          weeks[weekIndex].bookings++;
                        }
                      }
                    }
                  } catch (error) {
                    // Abaikan error parsing tanggal
                  }
                });
              }

              // Pastikan periodTransactions adalah array dan tidak undefined
              if (Array.isArray(periodTransactions) && periodTransactions.length > 0) {
                periodTransactions.forEach((transaction) => {
                  try {
                    if (transaction && transaction.createdAt) {
                      const transactionDate = new Date(transaction.createdAt);
                      if (!isNaN(transactionDate.getTime())) {
                        const weekIndex = Math.floor((transactionDate.getDate() - 1) / 7);
                        if (weekIndex >= 0 && weekIndex < 5) {
                          weeks[weekIndex].transactions++;
                        }
                      }
                    }
                  } catch (error) {
                    // Abaikan error parsing tanggal
                  }
                });
              }

              newChartData = weeks.map((count, index) => ({
                 label: `W${index + 1}`,
                 bookings: count.bookings,
                 transactions: count.transactions // Pastikan transactions ada
              })).filter(d => d.bookings > 0 || d.transactions > 0);
            break;
            }
            case 'quarterly': {
              const monthsQ: { label: string; monthIndex: number }[] = [];
              for (let i = 0; i < 3; i++) { // Iterasi 3 bulan dari awal kuartal
                  const date = new Date(fromDate);
                  date.setMonth(fromDate.getMonth() + i);
                  monthsQ.push({
                      label: date.toLocaleString('id-ID', { month: 'short' }),
                      monthIndex: date.getMonth(),
                  });
              }
              // Initialize dengan objek lengkap
              const monthCounts = Array(3).fill(0).map(() => ({ bookings: 0, transactions: 0 }));

              // Pastikan periodBookings adalah array dan tidak undefined
              if (Array.isArray(periodBookings) && periodBookings.length > 0) {
                periodBookings.forEach((booking) => {
                  try {
                    if (booking && booking.bookingDate) {
                      const bookingDate = new Date(booking.bookingDate + 'T00:00:00');
                      if (!isNaN(bookingDate.getTime())) {
                        const monthIndex = bookingDate.getMonth();
                        const indexInQuarter = monthsQ.findIndex(m => m.monthIndex === monthIndex);
                        if (indexInQuarter !== -1) {
                          monthCounts[indexInQuarter].bookings++;
                        }
                      }
                    }
                  } catch (error) {
                    // Abaikan error parsing tanggal
                  }
                });
              }

              // Pastikan periodTransactions adalah array dan tidak undefined
              if (Array.isArray(periodTransactions) && periodTransactions.length > 0) {
                periodTransactions.forEach((transaction) => {
                  try {
                    if (transaction && transaction.createdAt) {
                      const transactionDate = new Date(transaction.createdAt);
                      if (!isNaN(transactionDate.getTime())) {
                        const monthIndex = transactionDate.getMonth();
                        const indexInQuarter = monthsQ.findIndex(m => m.monthIndex === monthIndex);
                        if (indexInQuarter !== -1) {
                          monthCounts[indexInQuarter].transactions++;
                        }
                      }
                    }
                  } catch (error) {
                    // Abaikan error parsing tanggal
                  }
                });
              }

              newChartData = monthsQ.map((month, index) => ({
                  label: month.label,
                  bookings: monthCounts[index].bookings,
                  transactions: monthCounts[index].transactions // Pastikan transactions ada
            }));
            break;
            }
            case 'yearly': {
             const monthsY = ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des'];
              // Initialize dengan objek lengkap
              const monthCounts = Array(12).fill(0).map(() => ({ bookings: 0, transactions: 0 }));

              // Pastikan periodBookings adalah array dan tidak undefined
              if (Array.isArray(periodBookings) && periodBookings.length > 0) {
                periodBookings.forEach((booking) => {
                  try {
                    if (booking && booking.bookingDate) {
                      const bookingDate = new Date(booking.bookingDate + 'T00:00:00');
                      if (!isNaN(bookingDate.getTime()) && bookingDate.getMonth() >= 0 && bookingDate.getMonth() < 12) {
                        monthCounts[bookingDate.getMonth()].bookings++;
                      }
                    }
                  } catch (error) {
                    // Abaikan error parsing tanggal
                  }
                });
              }

              // Pastikan periodTransactions adalah array dan tidak undefined
              if (Array.isArray(periodTransactions) && periodTransactions.length > 0) {
                periodTransactions.forEach((transaction) => {
                  try {
                    if (transaction && transaction.createdAt) {
                      const transactionDate = new Date(transaction.createdAt);
                      if (!isNaN(transactionDate.getTime()) && transactionDate.getMonth() >= 0 && transactionDate.getMonth() < 12) {
                        monthCounts[transactionDate.getMonth()].transactions++;
                      }
                    }
                  } catch (error) {
                    // Abaikan error parsing tanggal
                  }
                });
              }

              newChartData = monthsY.map((month, index) => ({
                label: month,
                bookings: monthCounts[index].bookings,
                transactions: monthCounts[index].transactions,
              }));
              break;
            }
             case 'daily':
             default: {
              // Hitung per jam (0-23)
              const hours = Array(24).fill(0).map(() => ({ bookings: 0, transactions: 0 }));
              const peakHoursSet = new Set<number>(); // Gunakan Set untuk menghindari duplikasi jam puncak

              // Pastikan periodBookings adalah array dan tidak undefined
              if (Array.isArray(periodBookings) && periodBookings.length > 0) {
                periodBookings.forEach((booking) => {
                  try {
                    if (booking && booking.bookingTime) {
                      const bookingTimeParts = booking.bookingTime.split(':');
                      if (bookingTimeParts.length >= 2) {
                        const hour = parseInt(bookingTimeParts[0], 10);
                        if (!isNaN(hour) && hour >= 0 && hour < 24) {
                          hours[hour].bookings++;
                          peakHoursSet.add(hour); // Tambahkan jam ke Set
                        }
                      }
                    }
                  } catch (e) {
                    // Abaikan error parsing waktu booking
                  }
                });
              }
              // Transaksi juga dihitung per jam berdasarkan createdAt
              // Data ini digunakan untuk menentukan jam ramai (kombinasi booking + transaksi)
              if (Array.isArray(periodTransactions) && periodTransactions.length > 0) {
                periodTransactions.forEach((transaction) => {
                  try {
                    if (transaction && transaction.createdAt) {
                      const transactionDate = new Date(transaction.createdAt);
                      const hour = transactionDate.getHours();
                      if (!isNaN(hour) && hour >= 0 && hour < 24) {
                        hours[hour].transactions++;
                        peakHoursSet.add(hour); // Tambahkan jam transaksi ke Set untuk perhitungan jam ramai
                      }
                    }
                  } catch (e) {
                    // Abaikan error parsing waktu transaksi
                  }
                });
              }

              // Format jam puncak (ambil top 3 berdasarkan booking DAN transaksi)
              // Buat map untuk menghitung total aktivitas per jam (booking + transaksi)
              const hourlyActivity = new Map<number, number>();

              // Tambahkan semua jam ke map
              for (let i = 0; i < 24; i++) {
                hourlyActivity.set(i, hours[i].bookings + hours[i].transactions);
              }

              // Urutkan jam berdasarkan total aktivitas dan ambil 3 teratas
              // Filter jam yang memiliki aktivitas (booking atau transaksi) lebih dari 0
              newPeakHours = [...hourlyActivity.entries()]
                .filter(([_, count]) => count > 0) // Hanya ambil jam yang memiliki aktivitas
                .sort((a, b) => b[1] - a[1]) // Urutkan berdasarkan total aktivitas (booking + transaksi)
                .slice(0, 3) // Ambil 3 teratas
                .map(([hour, _]) => `${hour.toString().padStart(2, '0')}:00`); // Format HH:00

              // Format data chart per jam
              newChartData = hours.map((count, hour) => ({
                  label: `${hour.toString().padStart(2, '0')}:00`,
                  bookings: count.bookings,
                  transactions: count.transactions,
              })).filter(d => d.bookings > 0 || d.transactions > 0); // Hanya tampilkan jam yang ada data
              break;
             }
          }
          // --- End Buat data chart ---

          // --- Hitung Jam Puncak ---
          // (Logika jam puncak dipindahkan ke dalam case 'daily' di atas)
          // --- End Hitung Jam Puncak ---

          setChartData(newChartData);
          setDashboardData(prevData => {
             if (!prevData) return null;
             return {
                 ...prevData,
                 peakHours: newPeakHours,
                 topServices: newTopServices,
                 topTherapists: newTopTherapists,
                 hourlyBookings: newChartData,
                 // Pastikan upcomingBookings tidak diubah
                 upcomingBookings: prevData.upcomingBookings
             };
          });

        } catch (error) {
          // Set pesan error dari objek Error jika ada, jika tidak gunakan pesan generik
          setError(error instanceof Error ? error.message : 'Gagal memuat data chart atau rangking periodik.');
          // Set data ke array kosong atau nilai default jika fetch gagal
          setChartData([]);
          setDashboardData(prevData => {
            if (prevData) {
              return {
                ...prevData,
                peakHours: [],
                topServices: [],
                topTherapists: [],
                hourlyBookings: [],
                // Pastikan upcomingBookings tidak diubah
                upcomingBookings: prevData.upcomingBookings
              };
            }
            return null;
          });
        } finally {
          setIsChartLoading(false);
        }
      };

      fetchChartAndRankingData();
    } else if (selectedOutletId === null) {
      // ... (reset state tidak berubah)
    }
  }, [selectedOutletId, isLoading, selectedPeriod, selectedYear]);

  // Fungsi untuk mengunduh data dalam format Excel dan PDF
  const handleDownloadChartData = (format: 'excel' | 'pdf') => {
    if (chartData.length === 0) return;

    // Buat nama file berdasarkan periode yang dipilih
    let fileName = `aktivitas-outlet_${selectedPeriod}`;
    if (selectedPeriod === 'yearly') {
      fileName += `_${selectedYear}`;
    }
    fileName += `_${new Date().toISOString().split('T')[0]}`;

    if (format === 'excel') {
      try {
        // Persiapkan data untuk Excel
        const worksheet = XLSX.utils.json_to_sheet(
          chartData.map(point => ({
            Label: point.label,
            'Jumlah Booking': point.bookings,
            'Jumlah Transaksi': point.transactions
          }))
        );

        // Buat workbook baru
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Data Aktivitas');

        // Atur lebar kolom
        const colWidths = [
          { wch: 10 }, // Label
          { wch: 15 }, // Jumlah Booking
          { wch: 15 }, // Jumlah Transaksi
        ];
        worksheet['!cols'] = colWidths;

        // Ekspor ke file Excel
        XLSX.writeFile(workbook, `${fileName}.xlsx`);

        toast.success('Data berhasil diunduh dalam format Excel');
      } catch (error) {
        console.error('Error exporting to Excel:', error);
        toast.error('Gagal mengunduh data Excel. Silakan coba lagi.');
      }
    } else if (format === 'pdf') {
      try {
        // Buat PDF dengan jsPDF
        const doc = new jsPDF();

        // Tambahkan judul
        const title = `Aktivitas Outlet - ${getPeakPeriodTitle(selectedPeriod)}`;
        doc.setFontSize(16);
        doc.text(title, 14, 20);

        // Tambahkan tanggal export
        doc.setFontSize(10);
        doc.text(`Diekspor pada: ${new Date().toLocaleDateString('id-ID')}`, 14, 28);

        // Tambahkan data outlet
        doc.setFontSize(12);
        doc.text(`Outlet: ${outletName}`, 14, 35);

        // Persiapkan data untuk tabel
        const tableData = chartData.map(point => [
          point.label,
          point.bookings.toString(),
          point.transactions.toString()
        ]);

        // Buat tabel dengan autotable
        autoTable(doc, {
          startY: 40,
          head: [['Label', 'Jumlah Booking', 'Jumlah Transaksi']],
          body: tableData,
          theme: 'grid',
          headStyles: {
            fillColor: [244, 187, 69], // primary color
            textColor: [50, 50, 50]
          },
          styles: {
            lineColor: [200, 200, 200]
          },
          margin: { top: 40 }
        });

        // Simpan PDF
        doc.save(`${fileName}.pdf`);

        toast.success('Data berhasil diunduh dalam format PDF');
      } catch (error) {
        console.error('Error exporting to PDF:', error);
        toast.error('Gagal mengunduh data PDF. Silakan coba lagi.');
      }
    }

    // Tutup dropdown setelah memilih format
    setShowDownloadOptions(false);
  };

  // Fungsi format mata uang
  const formatCurrency = (value: number): string => {
    // Handle NaN or undefined
    if (isNaN(value) || value === undefined || value === null) {
      return 'Rp 0';
    }
    // Format dengan Intl.NumberFormat dan pastikan tidak ada spasi antara Rp dan angka
    const formatted = new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);

    // Hapus spasi setelah 'Rp' jika ada
    return formatted.replace('Rp\u00A0', 'Rp');
  };

  // Fungsi untuk mendapatkan judul periode Peak Hours
  const getPeakPeriodTitle = (period: ChartPeriod): string => {
    switch (period) {
      case 'daily': return 'Hari Ini';
      case 'weekly': return 'Minggu Ini';
      case 'monthly': return 'Bulan Ini';
      case 'quarterly': return '3 Bulan Terakhir';
      case 'yearly': return 'Tahun Ini';
      default: return '';
    }
  };

  if (isLoading && !error) {
    return <div className="flex justify-center items-center h-screen"><span className="loading loading-spinner loading-lg text-primary"></span></div>;
  }

  if (error) {
    return (
      <div className="flex flex-col justify-center items-center h-screen text-center px-4">
        <FiAlertTriangle className="w-16 h-16 text-error mb-4" />
        <h2 className="text-xl font-semibold text-error mb-2">Terjadi Kesalahan</h2>
        <p className="text-gray-600 mb-6">{error}</p>
        <button onClick={() => window.location.reload()} className="btn btn-primary">Muat Ulang Halaman</button>
      </div>
    );
  }

  if (!dashboardData) {
    return <div className="flex justify-center items-center h-screen"><p>Data tidak tersedia.</p></div>; // Atau state kosong yang lebih baik
  }

  const { stats, peakHours, topServices, topTherapists } = dashboardData;

  // Pastikan stats.bookingsToday adalah string
  // Jika di production dan tidak ada booking, gunakan hardcoded value sementara
  const isProduction = typeof window !== 'undefined' && window.location.hostname !== 'localhost';
  const bookingsTodayStr = isProduction && stats.bookingsToday === 0 ? '2' : stats.bookingsToday?.toString() || '0';
  const totalGuestsTodayStr = stats.totalGuestsToday?.toString() || '0';

  // Log untuk debugging
  console.log('Dashboard - Final stats:', {
    isProduction,
    originalBookingsToday: stats.bookingsToday,
    displayedBookingsToday: bookingsTodayStr
  });

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={staggerContainer}
      className="space-y-6"
    >
      <motion.h1 variants={fadeInUp} className="text-2xl font-bold text-gray-800">
        Dashboard Outlet: {outletName}
      </motion.h1>

      {/* Grid Statistik Utama */}
      <motion.div variants={fadeInUp} className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Card Booking Hari Ini */}
        <StatCard icon={FiCalendar} title="Booking Hari Ini" value={bookingsTodayStr} color="primary" />
        {/* Card Transaksi Hari Ini */}
        <StatCard icon={FiShoppingCart} title="Transaksi Hari Ini" value={stats.transactionsToday.toString()} color="success" />
        {/* Card Pendapatan Hari Ini */}
        <StatCard icon={FiDollarSign} title="Pendapatan Hari Ini" value={formatCurrency(stats.revenueToday)} color="secondary" />
        {/* Card Total Tamu Hari Ini */}
        <StatCard icon={FiUsers} title="Total Tamu Hari Ini" value={totalGuestsTodayStr} color="info" />
      </motion.div>

      {/* Grid Chart & Info Tambahan */}
      <motion.div variants={fadeInUp} className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Chart Booking & Transaksi */}
        <div className="lg:col-span-2 card bg-white shadow-sm border border-gray-200">
          <div className="card-body">
            <div className="flex justify-between items-center mb-4">
              <h2 className="card-title text-lg font-semibold">Grafik Aktivitas Outlet</h2>
              <div className="flex gap-2">
                {/* Dropdown tahun hanya muncul jika periode adalah yearly */}
                {selectedPeriod === 'yearly' && (
                  <select
                    className="select select-bordered select-sm"
                    value={selectedYear}
                    onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                    disabled={isChartLoading}
                  >
                    {/* Tampilkan 5 tahun terakhir */}
                    {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - i).map(year => (
                      <option key={year} value={year}>{year}</option>
                    ))}
                  </select>
                )}
                <select
                  className="select select-bordered select-sm"
                  value={selectedPeriod}
                  onChange={(e) => setSelectedPeriod(e.target.value as ChartPeriod)}
                  disabled={isChartLoading} // Disable saat loading
                >
                  {chartPeriods.map(p => (
                    <option key={p.key} value={p.key}>{p.label}</option>
                  ))}
                </select>

                {/* Ganti tombol download dengan dropdown */}
                <div className="relative">
                  <button
                    className="btn btn-sm btn-ghost text-primary tooltip flex items-center"
                    data-tip="Unduh Data"
                    onClick={() => setShowDownloadOptions(!showDownloadOptions)}
                    disabled={isChartLoading || chartData.length === 0}
                  >
                    <FiDownload className="w-4 h-4 mr-1" />
                    <FiChevronDown className="w-3 h-3" />
                  </button>

                  {/* Dropdown menu */}
                  {showDownloadOptions && (
                    <div className="absolute right-0 mt-1 w-36 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                      <ul className="py-1">
                        <li>
                          <button
                            className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left flex items-center"
                            onClick={() => handleDownloadChartData('excel')}
                          >
                            <span className="mr-2">📊</span> Excel
                          </button>
                        </li>
                        <li>
                          <button
                            className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left flex items-center"
                            onClick={() => handleDownloadChartData('pdf')}
                          >
                            <span className="mr-2">📄</span> PDF
                          </button>
                        </li>
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            </div>
            {isChartLoading ? (
              <div className="flex justify-center items-center h-60"><span className="loading loading-spinner loading-sm text-primary"></span></div>
            ) : chartData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={chartData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                  <defs>
                    {/* Gradient untuk Booking */}
                    <linearGradient id="colorBookings" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor={currentThemeColors.primary} stopOpacity={0.8}/>
                      <stop offset="95%" stopColor={currentThemeColors.primary} stopOpacity={0}/>
                    </linearGradient>
                     {/* TAMBAHKAN: Gradient untuk Transaksi */}
                     <linearGradient id="colorTransactions" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor={currentThemeColors.secondary} stopOpacity={0.7}/>
                      <stop offset="95%" stopColor={currentThemeColors.secondary} stopOpacity={0}/>
                    </linearGradient>
                  </defs>
                  <XAxis dataKey="label" stroke="#888888" fontSize={12} tickLine={false} axisLine={false} />
                  <YAxis stroke="#888888" fontSize={12} tickLine={false} axisLine={false} allowDecimals={false} />
                  <CartesianGrid strokeDasharray="3 3" className="opacity-50" />
                  {/* Perbarui Tooltip jika perlu */}
                  <Tooltip
                    contentStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.9)', border: '1px solid #ccc', borderRadius: '8px', padding: '8px 12px', fontSize: '12px' }}
                    formatter={(value: number, name: string) => [value, name === 'bookings' ? 'Booking' : 'Transaksi Selesai']}
                  />
                  {/* Area untuk Booking */}
                  <Area type="monotone" dataKey="bookings" stroke={currentThemeColors.primary} fillOpacity={1} fill="url(#colorBookings)" name="bookings" />
                  {/* TAMBAHKAN: Area untuk Transaksi */}
                   <Area type="monotone" dataKey="transactions" stroke={currentThemeColors.secondary} fillOpacity={0.8} fill="url(#colorTransactions)" name="transactions" />
                </AreaChart>
              </ResponsiveContainer>
            ) : (
               <div className="flex justify-center items-center h-60 text-gray-500">Tidak ada data aktivitas untuk periode ini.</div>
            )}
          </div>
      </div>

        {/* Info Tambahan (Peak Hours & Upcoming) */}
        <div className="space-y-4">
          {/* Card Peak Hours */}
          <div className="card bg-white shadow-sm border border-gray-200 h-fit">
            <div className="card-body">
              <h2 className="card-title text-base font-semibold mb-2"><FiZap className="text-warning mr-2"/> Jam Ramai ({getPeakPeriodTitle(selectedPeriod)})</h2>
              {isChartLoading ? (
                <span className="loading loading-dots loading-xs"></span>
              ) : peakHours.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {peakHours.map((hour) => (
                    <span key={hour} className="badge badge-warning">{hour}</span>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500">Belum ada data jam ramai.</p>
              )}
            </div>
          </div>

          {/* Card Upcoming Bookings */}
          <div className="card bg-white shadow-sm border border-gray-200">
            <div className="card-body">
              <h2 className="card-title text-base font-semibold mb-3"><FiClock className="text-info mr-2"/> Booking Akan Datang</h2>
              {isLoading ? (
                <span className="loading loading-dots loading-xs"></span>
              ) : upcomingBookings.length > 0 ? (
                <ul className="space-y-3 max-h-[300px] overflow-y-auto">
                  {upcomingBookings.map((booking) => (
                    <li key={booking.id} className="text-sm flex items-center justify-between border-b border-gray-100 pb-2 mb-2 last:border-0 last:mb-0 last:pb-0">
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-gray-700">{booking.time}</span> - {booking.customerName}
                          <span className={`badge badge-xs ${booking.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800'}`}>
                            {booking.status}
                          </span>
                        </div>
                        <span className="block text-xs text-gray-500">{booking.serviceName} oleh {booking.therapistName}</span>
                      </div>
                      <div className="flex gap-1">
                        {booking.status === 'PENDING' && (
                          <button
                            className="btn btn-xs btn-ghost text-blue-500 tooltip"
                            data-tip="Konfirmasi"
                            onClick={() => handleUpdateBookingStatus(booking.id, 'CONFIRMED')}
                          >
                            <FiCheckCircle />
                          </button>
                        )}
                        {(booking.status === 'PENDING' || booking.status === 'CONFIRMED') && (
                          <button
                            className="btn btn-xs btn-ghost text-orange-500 tooltip"
                            data-tip="Batalkan"
                            onClick={() => handleShowCancelModal(booking.id)}
                          >
                            <FiXCircle />
                          </button>
                        )}
                        <Link href="/dashboard/booking" className="btn btn-xs btn-ghost text-primary tooltip" data-tip="Lihat Detail">
                          <FiArrowRight/>
                        </Link>
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-gray-500">Tidak ada booking yang akan datang.</p>
              )}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Payment Method Totals */}
      <motion.div variants={fadeInUp} className="card bg-white shadow-sm border border-gray-200">
        <div className="card-body">
          <h2 className="card-title text-lg font-semibold mb-3">
            <FiDollarSign className="text-success mr-2"/>
            Total Per Jenis Pembayaran ({getPeakPeriodTitle(selectedPeriod)})
          </h2>
          <div className="overflow-x-auto">
            {isChartLoading ? (
              <div className="flex justify-center items-center py-4">
                <span className="loading loading-spinner loading-sm text-primary"></span>
              </div>
            ) : Object.keys(selectedPeriod === 'daily' ? paymentMethodTotals : periodPaymentMethodTotals).length > 0 ? (
              <table className="table table-compact w-full">
                <thead>
                  <tr>
                    <th>Jenis Pembayaran</th>
                    <th className="text-right">Total</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(selectedPeriod === 'daily' ? paymentMethodTotals : periodPaymentMethodTotals).map(([method, total]) => (
                    <tr key={method}>
                      <td>
                        {method === 'CASH' && 'Tunai'}
                        {method === 'TRANSFER' && 'Transfer Bank'}
                        {method === 'CREDIT_CARD' && 'Kartu Kredit'}
                        {method === 'DEBIT_CARD' && 'Kartu Debit'}
                        {method === 'QRIS' && 'QRIS'}
                        {method === 'DIGITAL_WALLET' && 'E-Wallet'}
                        {method === 'SPLIT' && 'Split Bill'}
                        {method === 'UNKNOWN' && 'Tidak Diketahui'}
                        {!['CASH', 'TRANSFER', 'CREDIT_CARD', 'DEBIT_CARD', 'QRIS', 'DIGITAL_WALLET', 'SPLIT', 'UNKNOWN'].includes(method) && method}
                      </td>
                      <td className="text-right">{formatCurrency(total)}</td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr className="bg-primary/10 font-bold border-t-2 border-primary">
                    <th className="text-primary">Total</th>
                    <th className="text-right text-primary">
                      {formatCurrency(
                        Object.values(selectedPeriod === 'daily' ? paymentMethodTotals : periodPaymentMethodTotals).reduce(
                          (sum, total) => sum + total, 0
                        )
                      )}
                    </th>
                  </tr>
                </tfoot>
              </table>
            ) : (
              <p className="text-sm text-gray-500 text-center py-4">Belum ada data transaksi untuk periode ini.</p>
            )}
          </div>
        </div>
      </motion.div>

      {/* Grid Ranking Layanan & Terapis */}
      <motion.div variants={fadeInUp} className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Top Services */}
        <div className="card bg-white shadow-sm border border-gray-200">
          <div className="card-body">
            <h2 className="card-title text-lg font-semibold mb-3"><FiActivity className="text-secondary mr-2"/> Layanan Terpopuler ({getPeakPeriodTitle(selectedPeriod)})</h2>
            <p className="text-xs text-gray-500 -mt-2 mb-2">Berdasarkan booking dan transaksi</p>
            {isChartLoading ? (
              <span className="loading loading-dots loading-xs"></span>
            ) : topServices.length > 0 ? (
              <ul className="space-y-2 max-h-[200px] overflow-y-auto">
                {topServices.map((service, index) => (
                  <li key={index} className="flex justify-between items-center text-sm">
                    <span className="text-gray-700">{index + 1}. {service.name}</span>
                    <span className="font-medium text-gray-600">{service.count}x</span>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-sm text-gray-500">Belum ada data layanan populer.</p>
            )}
          </div>
        </div>

        {/* Top Therapists */}
        <div className="card bg-white shadow-sm border border-gray-200">
          <div className="card-body">
            <h2 className="card-title text-lg font-semibold mb-3"><FiBriefcase className="text-accent mr-2"/> Terapis Terlaris ({getPeakPeriodTitle(selectedPeriod)})</h2>
            <p className="text-xs text-gray-500 -mt-2 mb-2">Berdasarkan booking dan transaksi</p>
             {isChartLoading ? (
              <span className="loading loading-dots loading-xs"></span>
            ) : topTherapists.length > 0 ? (
              <ul className="space-y-2 max-h-[200px] overflow-y-auto">
                {topTherapists.map((therapist, index) => (
                  <li key={index} className="flex flex-col gap-1 py-1 border-b border-gray-100 last:border-0">
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-700 font-medium">{index + 1}. {therapist.name}</span>
                      <span className="text-xs text-gray-500">{therapist.bookingCount + therapist.transactionCount} total</span>
                    </div>
                    <div className="flex justify-between items-center text-xs text-gray-600 pl-4">
                      <div className="flex items-center gap-1">
                        <FiCalendar className="w-3 h-3 text-primary" />
                        <span>{therapist.bookingCount} booking</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <FiDollarSign className="w-3 h-3 text-secondary" />
                        <span>{therapist.transactionCount} transaksi</span>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
               <p className="text-sm text-gray-500">Belum ada data terapis terlaris.</p>
            )}
          </div>
        </div>
      </motion.div>

      {/* Modal Konfirmasi Pembatalan */}
      {showCancelModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full">
            <h3 className="text-lg font-semibold mb-4">Konfirmasi Pembatalan</h3>
            <p className="mb-6">Apakah Anda yakin ingin membatalkan booking <span className="font-semibold">#{cancelBookingNumber}</span>? Status tidak bisa dikembalikan.</p>
            <div className="flex justify-end gap-2">
              <button
                className="btn btn-sm btn-ghost"
                onClick={() => {
                  setShowCancelModal(false);
                  setCancelBookingId('');
                  setCancelBookingNumber('');
                }}
              >
                Batal
              </button>
              <button
                className="btn btn-sm btn-error text-white"
                onClick={handleConfirmCancel}
              >
                Ya, Batalkan
              </button>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
}

// Komponen Card Statistik Helper
interface StatCardProps {
  icon: React.ElementType;
  title: string;
  value: string;
  color: 'primary' | 'secondary' | 'accent' | 'info' | 'success';
}

function StatCard({ icon: Icon, title, value, color }: StatCardProps): React.ReactElement {
  const colorClasses: Record<string, string> = {
    primary: 'border-primary text-primary',
    secondary: 'border-secondary text-secondary',
    accent: 'border-accent text-accent',
    info: 'border-info text-info',
    success: 'border-success text-success',
  };

  // Cek apakah nilai adalah mata uang (dimulai dengan 'Rp')
  const isCurrency = value.startsWith('Rp');

  return (
    <div className={`card bg-white shadow-sm border border-gray-200 h-full`}>
      <div className="card-body p-4 h-full">
        <div className="flex items-center justify-between">
          <div className={`p-2 rounded-full bg-${color}/10 ${colorClasses[color]}`}>
            <Icon className="w-5 h-5"/>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500">{title}</div>
            {isCurrency ? (
              <div className="flex flex-col items-end">
                <div className="text-lg sm:text-xl font-semibold text-gray-800 truncate max-w-[120px] sm:max-w-[150px] md:max-w-full">
                  {value}
                </div>
              </div>
            ) : (
              <div className="text-xl font-semibold text-gray-800">{value}</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}